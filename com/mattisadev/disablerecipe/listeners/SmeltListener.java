package com.mattisadev.disablerecipe.listeners;

import com.mattisadev.disablerecipe.DisableRecipe;
import com.mattisadev.disablerecipe.api.enums.LimitationType;
import com.mattisadev.disablerecipe.api.events.SmeltingCancelEvent;
import com.mattisadev.disablerecipe.core.compatibility.ServerVersion;
import com.mattisadev.disablerecipe.core.compatibility.XMaterial;
import com.mattisadev.disablerecipe.core.utils.PlayerUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.block.BlockState;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.inventory.InventoryMoveItemEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.InventoryHolder;

public class SmeltListener implements Listener {
   private final DisableRecipe plugin;

   public SmeltListener(DisableRecipe plugin) {
      this.plugin = plugin;
   }

   @EventHandler
   public void onInventoryClick(InventoryClickEvent event) {
      Player player = (Player)event.getWhoClicked();
      DisableRecipe var10000 = this.plugin;
      if (DisableRecipe.getAPI().isSmeltingEnabled()) {
         if (event.getClickedInventory() != null && event.getView().getTopInventory() != null) {
            if (event.getInventory().getType().equals(InventoryType.FURNACE) || event.getClickedInventory().getType().equals(InventoryType.FURNACE) || ServerVersion.isServerVersionAtLeast(ServerVersion.V1_14) && (event.getInventory().getType().equals(InventoryType.BLAST_FURNACE) || event.getClickedInventory().getType().equals(InventoryType.BLAST_FURNACE) || event.getInventory().getType().equals(InventoryType.SMOKER) || event.getClickedInventory().getType().equals(InventoryType.SMOKER))) {
               var10000 = this.plugin;
               DisableRecipe.getAPI().getLimitations().forEach((limitations) -> {
                  if (limitations.getType().equals(LimitationType.SMELTING) && (limitations.disableAllWorlds() || limitations.getWorlds().contains(player.getLocation().getWorld().getName()))) {
                     if (event.getClickedInventory().equals(event.getView().getBottomInventory()) && event.isShiftClick()) {
                        if (limitations.disableAllItems() && event.getCurrentItem().getType() != XMaterial.AIR.parseMaterial() || limitations.getItem() != null && ((XMaterial)XMaterial.matchXMaterial(limitations.getItem()).get()).isSimilar(event.getCurrentItem())) {
                           event.setCancelled(true);
                           Bukkit.getServer().getPluginManager().callEvent(new SmeltingCancelEvent(limitations, player));
                           if (!this.plugin.getMessages().getString("smelting.cannot-smelt").isEmpty()) {
                              player.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("smelting.cannot-smelt")));
                           }

                           return;
                        }
                     } else if (event.getClickedInventory().equals(event.getView().getTopInventory()) && (limitations.disableAllItems() && event.getCursor().getType() != XMaterial.AIR.parseMaterial() || limitations.getItem() != null && ((XMaterial)XMaterial.matchXMaterial(limitations.getItem()).get()).isSimilar(event.getCursor()))) {
                        event.setCancelled(true);
                        Bukkit.getServer().getPluginManager().callEvent(new SmeltingCancelEvent(limitations, player));
                        if (!this.plugin.getMessages().getString("smelting.cannot-smelt").isEmpty()) {
                           player.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("smelting.cannot-smelt")));
                        }

                        return;
                     }
                  }

               });
            }

         }
      }
   }

   @EventHandler
   public void onHopperTransfer(InventoryMoveItemEvent event) {
      DisableRecipe var10000;
      if (event.getSource().getType() == InventoryType.PLAYER && (event.getDestination().getType() == InventoryType.FURNACE || ServerVersion.isServerVersionAtLeast(ServerVersion.V1_14) && (event.getDestination().getType() == InventoryType.SMOKER || event.getDestination().getType() == InventoryType.BLAST_FURNACE))) {
         var10000 = this.plugin;
         if (!DisableRecipe.getAPI().isSmeltingEnabled()) {
            return;
         }

         var10000 = this.plugin;
         DisableRecipe.getAPI().getLimitations().forEach((limitations) -> {
            if (limitations.getType().equals(LimitationType.SMELTING)) {
               InventoryHolder eventHolder = event.getDestination().getHolder();
               if (eventHolder instanceof BlockState) {
                  Location hopper = ((BlockState)eventHolder).getLocation();
                  if ((limitations.disableAllWorlds() || limitations.getWorlds().contains(hopper.getWorld().getName())) && (limitations.disableAllItems() || limitations.getItem() != null && ((XMaterial)XMaterial.matchXMaterial(limitations.getItem()).get()).isSimilar(event.getItem()))) {
                     event.setCancelled(true);
                     return;
                  }
               }
            }

         });
      }

      if (event.getSource().getType() == InventoryType.HOPPER && (event.getDestination().getType() == InventoryType.FURNACE || ServerVersion.isServerVersionAtLeast(ServerVersion.V1_14) && (event.getDestination().getType() == InventoryType.SMOKER || event.getDestination().getType() == InventoryType.BLAST_FURNACE))) {
         var10000 = this.plugin;
         if (!DisableRecipe.getAPI().isSmeltingEnabled()) {
            return;
         }

         var10000 = this.plugin;
         DisableRecipe.getAPI().getLimitations().forEach((limitations) -> {
            if (limitations.getType().equals(LimitationType.SMELTING)) {
               InventoryHolder eventHolder = event.getDestination().getHolder();
               if (eventHolder instanceof BlockState) {
                  Location hopper = ((BlockState)eventHolder).getLocation();
                  if ((limitations.disableAllWorlds() || limitations.getWorlds().contains(hopper.getWorld().getName())) && (limitations.disableAllItems() || limitations.getItem() != null && ((XMaterial)XMaterial.matchXMaterial(limitations.getItem()).get()).isSimilar(event.getItem()))) {
                     event.setCancelled(true);
                     return;
                  }
               }
            }

         });
      }

   }

   @EventHandler
   public void onInventoryDrag(InventoryDragEvent event) {
      Player player = (Player)event.getWhoClicked();
      DisableRecipe var10000 = this.plugin;
      if (DisableRecipe.getAPI().isSmeltingEnabled()) {
         if (event.getView().getTopInventory() != null) {
            if (event.getView().getTopInventory().getType().equals(InventoryType.FURNACE) || ServerVersion.isServerVersionAtLeast(ServerVersion.V1_14) && (event.getView().getTopInventory().getType().equals(InventoryType.BLAST_FURNACE) || event.getView().getTopInventory().getType().equals(InventoryType.SMOKER))) {
               event.getRawSlots().stream().forEach((slot) -> {
                  if (slot < 3) {
                     DisableRecipe var10000 = this.plugin;
                     DisableRecipe.getAPI().getLimitations().forEach((limitations) -> {
                        if (limitations.getType().equals(LimitationType.SMELTING) && (limitations.disableAllWorlds() || limitations.getWorlds().contains(player.getLocation().getWorld().getName()))) {
                           if (event.getCursor() == null) {
                              if (limitations.disableAllItems() || limitations.getItem() != null && ((XMaterial)XMaterial.matchXMaterial(limitations.getItem()).get()).isSimilar(event.getOldCursor())) {
                                 event.setCancelled(true);
                                 Bukkit.getServer().getPluginManager().callEvent(new SmeltingCancelEvent(limitations, player));
                                 if (!this.plugin.getMessages().getString("smelting.cannot-smelt").isEmpty()) {
                                    player.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("smelting.cannot-smelt")));
                                 }

                                 return;
                              }
                           } else if (limitations.disableAllItems() || limitations.getItem() != null && ((XMaterial)XMaterial.matchXMaterial(limitations.getItem()).get()).isSimilar(event.getCursor())) {
                              event.setCancelled(true);
                              Bukkit.getServer().getPluginManager().callEvent(new SmeltingCancelEvent(limitations, player));
                              if (!this.plugin.getMessages().getString("smelting.cannot-smelt").isEmpty()) {
                                 player.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("smelting.cannot-smelt")));
                              }

                              return;
                           }
                        }

                     });
                  }

               });
            }

         }
      }
   }

   @EventHandler
   public void onCampfireClick(PlayerInteractEvent event) {
      Player player = event.getPlayer();
      DisableRecipe var10000 = this.plugin;
      if (DisableRecipe.getAPI().isSmeltingEnabled()) {
         if (!ServerVersion.isServerVersionBelow(ServerVersion.V1_14) && event.getClickedBlock() != null && event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            if (XMaterial.CAMPFIRE.parseMaterial() == XMaterial.matchXMaterial(event.getClickedBlock().getType()).parseMaterial()) {
               var10000 = this.plugin;
               DisableRecipe.getAPI().getLimitations().forEach((limitations) -> {
                  if (limitations.getType().equals(LimitationType.SMELTING) && (limitations.disableAllWorlds() || limitations.getWorlds().contains(player.getLocation().getWorld().getName()))) {
                     if (PlayerUtils.getHeldItem(player) != null && PlayerUtils.getHeldItem(player) != XMaterial.AIR.parseItem()) {
                        if (limitations.disableAllItems() || limitations.getItem() != null && ((XMaterial)XMaterial.matchXMaterial(limitations.getItem()).get()).isSimilar(PlayerUtils.getHeldItem(player))) {
                           event.setCancelled(true);
                           Bukkit.getServer().getPluginManager().callEvent(new SmeltingCancelEvent(limitations, player));
                           if (!this.plugin.getMessages().getString("smelting.cannot-smelt").isEmpty()) {
                              player.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("smelting.cannot-smelt")));
                           }

                           return;
                        }
                     } else if (PlayerUtils.getOffHeldItem(player) != null && PlayerUtils.getOffHeldItem(player) != XMaterial.AIR.parseItem() && (limitations.disableAllItems() || limitations.getItem() != null && ((XMaterial)XMaterial.matchXMaterial(limitations.getItem()).get()).isSimilar(PlayerUtils.getOffHeldItem(player)))) {
                        event.setCancelled(true);
                        Bukkit.getServer().getPluginManager().callEvent(new SmeltingCancelEvent(limitations, player));
                        if (!this.plugin.getMessages().getString("smelting.cannot-smelt").isEmpty()) {
                           player.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("smelting.cannot-smelt")));
                        }

                        return;
                     }
                  }

               });
            }
         }
      }
   }
}
