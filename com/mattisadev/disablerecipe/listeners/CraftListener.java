package com.mattisadev.disablerecipe.listeners;

import com.mattisadev.disablerecipe.DisableRecipe;
import com.mattisadev.disablerecipe.api.enums.LimitationType;
import com.mattisadev.disablerecipe.api.events.CraftingCancelEvent;
import com.mattisadev.disablerecipe.core.compatibility.XMaterial;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.CraftItemEvent;

public class CraftListener implements Listener {
   private final DisableRecipe plugin;

   public CraftListener(DisableRecipe plugin) {
      this.plugin = plugin;
   }

   @EventHandler
   public void onItemCraft(CraftItemEvent event) {
      Player player = (Player)event.getWhoClicked();
      DisableRecipe var10000 = this.plugin;
      if (DisableRecipe.getAPI().isCraftingEnabled()) {
         var10000 = this.plugin;
         DisableRecipe.getAPI().getLimitations().forEach((limitations) -> {
            if (limitations.getType().equals(LimitationType.CRAFTING) && (limitations.disableAllWorlds() || limitations.getWorlds().contains(player.getLocation().getWorld().getName()))) {
               if (event.getInventory().getResult() == null || event.getInventory().getResult().getType() == XMaterial.AIR.parseMaterial()) {
                  return;
               }

               if (limitations.disableAllItems() || limitations.getItem() != null && ((XMaterial)XMaterial.matchXMaterial(limitations.getItem()).get()).isSimilar(event.getInventory().getResult())) {
                  if (limitations.hasBypassPermission()) {
                     if (player.hasPermission(limitations.getBypassPermission())) {
                        return;
                     }

                     event.setCancelled(true);
                     Bukkit.getServer().getPluginManager().callEvent(new CraftingCancelEvent(limitations, player));
                     event.getInventory().setResult(XMaterial.AIR.parseItem());
                     if (!this.plugin.getMessages().getString("crafting.cannot-craft").isEmpty()) {
                        player.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("crafting.cannot-craft")));
                     }
                  } else {
                     event.setCancelled(true);
                     Bukkit.getServer().getPluginManager().callEvent(new CraftingCancelEvent(limitations, player));
                     event.getInventory().setResult(XMaterial.AIR.parseItem());
                     if (!this.plugin.getMessages().getString("crafting.cannot-craft").isEmpty()) {
                        player.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("crafting.cannot-craft")));
                     }
                  }
               }
            }

         });
      }
   }
}
