package com.mattisadev.disablerecipe;

import com.mattisadev.disablerecipe.api.DisableRecipeAPI;
import com.mattisadev.disablerecipe.commands.CommandManager;
import com.mattisadev.disablerecipe.core.configuration.ConfigManager;
import com.mattisadev.disablerecipe.listeners.CraftListener;
import com.mattisadev.disablerecipe.listeners.SmeltListener;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.plugin.java.JavaPlugin;

public class DisableRecipe extends JavaPlugin {
   private ConfigManager configManager;
   private CommandManager commandManager = new CommandManager(this);
   private static DisableRecipeAPI disableRecipeAPI;

   public void onEnable() {
      this.configManager = new ConfigManager(this);
      this.configManager.load("config.yml");
      this.configManager.save("config.yml");
      this.configManager.load("messages.yml");
      this.configManager.save("messages.yml");
      this.commandManager.setup();
      disableRecipeAPI = new DisableRecipeAPI(this);
      disableRecipeAPI.loadLimitations();
      Bukkit.getPluginManager().registerEvents(new CraftListener(this), this);
      Bukkit.getPluginManager().registerEvents(new SmeltListener(this), this);
   }

   public static DisableRecipeAPI getAPI() {
      return disableRecipeAPI;
   }

   public ConfigManager getConfigManager() {
      return this.configManager;
   }

   public FileConfiguration getConfig() {
      ConfigManager var10000 = this.configManager;
      return ConfigManager.get("config.yml");
   }

   public FileConfiguration getMessages() {
      ConfigManager var10000 = this.configManager;
      return ConfigManager.get("messages.yml");
   }
}
