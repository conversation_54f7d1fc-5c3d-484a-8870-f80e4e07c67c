package com.mattisadev.disablerecipe.commands;

import com.mattisadev.disablerecipe.DisableRecipe;
import org.bukkit.command.CommandSender;

public abstract class SubManager {
   private final DisableRecipe plugin;

   public SubManager(DisableRecipe plugin) {
      this.plugin = plugin;
   }

   public abstract void onCommand(CommandSender var1, String[] var2, DisableRecipe var3);

   public abstract String name();

   public abstract String info();

   public abstract String[] aliases();
}
