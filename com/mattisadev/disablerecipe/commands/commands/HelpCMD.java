package com.mattisadev.disablerecipe.commands.commands;

import com.mattisadev.disablerecipe.DisableRecipe;
import com.mattisadev.disablerecipe.commands.SubManager;
import java.util.Iterator;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;

public class HelpCMD extends SubManager {
   public HelpCMD(DisableRecipe plugin) {
      super(plugin);
   }

   public void onCommand(CommandSender sender, String[] args, DisableRecipe plugin) {
      if (args.length == 1) {
         if (!sender.hasPermission("disablerecipe.cmd.help") && !sender.hasPermission("disablerecipe.*")) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("no-permission")));
         } else {
            Iterator var4 = plugin.getMessages().getStringList("commands.help").iterator();

            while(var4.hasNext()) {
               String str = (String)var4.next();
               sender.sendMessage(ChatColor.translateAlternateColorCodes('&', str));
            }
         }
      } else {
         sender.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("commands.error")));
      }

   }

   public String name() {
      return "help";
   }

   public String info() {
      return "Shows this help message";
   }

   public String[] aliases() {
      return new String[0];
   }
}
