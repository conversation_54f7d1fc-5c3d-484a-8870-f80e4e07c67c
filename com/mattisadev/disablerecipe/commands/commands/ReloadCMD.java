package com.mattisadev.disablerecipe.commands.commands;

import com.mattisadev.disablerecipe.DisableRecipe;
import com.mattisadev.disablerecipe.commands.SubManager;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;

public class ReloadCMD extends SubManager {
   public ReloadCMD(DisableRecipe plugin) {
      super(plugin);
   }

   public void onCommand(CommandSender sender, String[] args, DisableRecipe plugin) {
      if (args.length == 1) {
         if (!sender.hasPermission("disablerecipe.cmd.reload") && !sender.hasPermission("disablerecipe.*")) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("no-permission")));
         } else {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("commands.reload.success")));
            plugin.getConfigManager().reload("config.yml");
            plugin.getConfigManager().reload("messages.yml");
            DisableRecipe.getAPI().loadLimitations();
         }
      } else {
         sender.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("commands.error")));
      }

   }

   public String name() {
      return "reload";
   }

   public String info() {
      return "Reload the plugin's files";
   }

   public String[] aliases() {
      return new String[0];
   }
}
