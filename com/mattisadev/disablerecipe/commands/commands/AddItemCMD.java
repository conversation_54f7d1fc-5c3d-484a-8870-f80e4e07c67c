package com.mattisadev.disablerecipe.commands.commands;

import com.mattisadev.disablerecipe.DisableRecipe;
import com.mattisadev.disablerecipe.commands.SubManager;
import com.mattisadev.disablerecipe.core.compatibility.XMaterial;
import com.mattisadev.disablerecipe.core.utils.NumberUtils;
import com.mattisadev.disablerecipe.core.utils.PlayerUtils;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class AddItemCMD extends SubManager {
   public AddItemCMD(DisableRecipe plugin) {
      super(plugin);
   }

   public void onCommand(CommandSender sender, String[] args, DisableRecipe plugin) {
      if (!(sender instanceof Player)) {
         sender.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("only-players")));
      } else {
         Player player = (Player)sender;
         if (player.hasPermission("disablerecipe.cmd.additem") && player.hasPermission("disablerecipe.*")) {
            if (args.length == 3) {
               if (PlayerUtils.getHeldItem(player).getType().equals(XMaterial.AIR.parseMaterial())) {
                  player.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("commands.add-item.holding-air")));
               } else {
                  ItemStack item = PlayerUtils.getHeldItem(player);
                  int randomNumber = NumberUtils.randomInt(1, 100000);
                  String[] worlds = args[2].split(",");
                  String var8 = args[1].toUpperCase();
                  byte var9 = -1;
                  switch(var8.hashCode()) {
                  case 64383488:
                     if (var8.equals("CRAFT")) {
                        var9 = 0;
                     }
                     break;
                  case 79014899:
                     if (var8.equals("SMELT")) {
                        var9 = 1;
                     }
                  }

                  switch(var9) {
                  case 0:
                     if (DisableRecipe.getAPI().getCraftingItems().contains(item.getType().toString())) {
                        player.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("commands.add-item.already-added")));
                     } else {
                        plugin.getConfig().set("crafting.items." + randomNumber + ".material", XMaterial.matchXMaterial(item.getType()).toString().replace(" ", "_").toUpperCase());
                        plugin.getConfig().set("crafting.items." + randomNumber + ".bypass-permission", "disablerecipe." + XMaterial.matchXMaterial(item.getType()).toString().replace(" ", "_").toLowerCase());
                        plugin.getConfig().set("crafting.items." + randomNumber + ".worlds", worlds);
                        plugin.saveConfig();
                        player.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("commands.add-item.success").replace("{type}", "crafting").replace("{world}", args[2].replace(",", ", ")).replace("{item}", XMaterial.matchXMaterial(item.getType()).toString().replace(" ", "_").toUpperCase())));
                     }
                     break;
                  case 1:
                     if (DisableRecipe.getAPI().getSmeltingItems().contains(item.getType().toString())) {
                        player.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("commands.add-item.already-added")));
                     } else {
                        plugin.getConfig().set("smelting.items." + randomNumber + ".material", XMaterial.matchXMaterial(item.getType()).toString().replace(" ", "_").toUpperCase());
                        plugin.getConfig().set("smelting.items." + randomNumber + ".worlds", worlds);
                        plugin.saveConfig();
                        player.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("commands.add-item.success").replace("{type}", "smelting").replace("{world}", args[2].replace(",", ", ")).replace("{item}", XMaterial.matchXMaterial(item.getType()).toString().replace(" ", "_").toUpperCase())));
                     }
                     break;
                  default:
                     player.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("invalid-type")));
                  }

               }
            } else {
               player.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("commands.error")));
            }
         } else {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', plugin.getMessages().getString("no-permission")));
         }
      }
   }

   public String name() {
      return "additem";
   }

   public String info() {
      return "Adds the item you're holding into the config file";
   }

   public String[] aliases() {
      return new String[0];
   }
}
