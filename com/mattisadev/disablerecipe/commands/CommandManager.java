package com.mattisadev.disablerecipe.commands;

import com.mattisadev.disablerecipe.DisableRecipe;
import com.mattisadev.disablerecipe.commands.commands.AddItemCMD;
import com.mattisadev.disablerecipe.commands.commands.HelpCMD;
import com.mattisadev.disablerecipe.commands.commands.ReloadCMD;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;

public class CommandManager implements CommandExecutor {
   private ArrayList<SubManager> commands = new ArrayList();
   private final DisableRecipe plugin;
   public String main = "disablerecipe";

   public CommandManager(DisableRecipe plugin) {
      this.plugin = plugin;
   }

   public void setup() {
      this.plugin.getCommand(this.main).setExecutor(this);
      this.commands.add(new ReloadCMD(this.plugin));
      this.commands.add(new HelpCMD(this.plugin));
      this.commands.add(new AddItemCMD(this.plugin));
   }

   public boolean onCommand(CommandSender sender, Command command, String s, String[] args) {
      if (command.getName().equalsIgnoreCase(this.main)) {
         if (args.length == 0) {
            if (!sender.hasPermission("disablerecipe.cmd.help") && !sender.hasPermission("disablerecipe.*")) {
               sender.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("no-permission")));
            } else {
               Iterator var9 = this.plugin.getMessages().getStringList("commands.help").iterator();

               while(var9.hasNext()) {
                  String str = (String)var9.next();
                  sender.sendMessage(ChatColor.translateAlternateColorCodes('&', str));
               }
            }

            return true;
         }

         SubManager target = this.get(args[0]);
         if (target == null) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("commands.error")));
            return true;
         }

         ArrayList<String> arrayList = new ArrayList();
         arrayList.addAll(Arrays.asList(args));
         arrayList.remove(0);

         try {
            target.onCommand(sender, args, this.plugin);
         } catch (Exception var8) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&', this.plugin.getMessages().getString("commands.error")));
            var8.printStackTrace();
         }
      }

      return true;
   }

   private SubManager get(String name) {
      Iterator SubManagers = this.commands.iterator();

      while(SubManagers.hasNext()) {
         SubManager sc = (SubManager)SubManagers.next();
         if (sc.name().equalsIgnoreCase(name)) {
            return sc;
         }

         String[] aliases;
         int length = (aliases = sc.aliases()).length;

         for(int var5 = 0; var5 < length; ++var5) {
            String alias = aliases[var5];
            if (name.equalsIgnoreCase(alias)) {
               return sc;
            }
         }
      }

      return null;
   }
}
