package com.mattisadev.disablerecipe.api;

import com.mattisadev.disablerecipe.DisableRecipe;
import com.mattisadev.disablerecipe.api.enums.LimitationType;
import com.mattisadev.disablerecipe.api.objects.Limitations;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.bukkit.Bukkit;

public class DisableRecipeAPI {
   private final DisableRecipe plugin;
   private final List<Limitations> limitations = new ArrayList();

   public DisableRecipeAPI(DisableRecipe plugin) {
      this.plugin = plugin;
   }

   public void loadLimitations() {
      this.limitations.clear();
      this.plugin.getConfig().getConfigurationSection("crafting.items").getKeys(false).forEach((items) -> {
         Limitations limitations = new Limitations();
         limitations.setType(LimitationType.CRAFTING);
         if (!this.plugin.getConfig().getStringList("crafting.items." + items + ".worlds").isEmpty()) {
            ArrayList<String> enabledWorlds = new ArrayList();
            Iterator var4 = this.plugin.getConfig().getStringList("crafting.items." + items + ".worlds").iterator();

            while(var4.hasNext()) {
               String world = (String)var4.next();
               if (world.equalsIgnoreCase("*")) {
                  limitations.setDisableAllWorlds(true);
               }

               if (Bukkit.getWorld(world) != null) {
                  enabledWorlds.add(world);
               }
            }

            limitations.setWorlds(enabledWorlds);
            if (this.plugin.getConfig().contains("crafting.items." + items + ".material")) {
               if (this.plugin.getConfig().contains("crafting.items." + items + ".bypass-permission")) {
                  limitations.setBypassPermission(this.plugin.getConfig().getString("crafting.items." + items + ".bypass-permission"));
                  limitations.hasBypassPermission(true);
               }

               if (this.plugin.getConfig().getString("crafting.items." + items + ".material").equals("*")) {
                  limitations.setDisableAllItems(true);
               } else {
                  limitations.setItem(this.plugin.getConfig().getString("crafting.items." + items + ".material"));
               }

               this.limitations.add(limitations);
            }

         }
      });
      this.plugin.getConfig().getConfigurationSection("smelting.items").getKeys(false).forEach((items) -> {
         Limitations limitations = new Limitations();
         limitations.setType(LimitationType.SMELTING);
         if (!this.plugin.getConfig().getStringList("smelting.items." + items + ".worlds").isEmpty()) {
            ArrayList<String> enabledWorlds = new ArrayList();
            Iterator var4 = this.plugin.getConfig().getStringList("smelting.items." + items + ".worlds").iterator();

            while(var4.hasNext()) {
               String world = (String)var4.next();
               if (world.equalsIgnoreCase("*")) {
                  limitations.setDisableAllWorlds(true);
               }

               if (Bukkit.getWorld(world) != null) {
                  enabledWorlds.add(world);
               }
            }

            limitations.setWorlds(enabledWorlds);
            if (this.plugin.getConfig().contains("smelting.items." + items + ".material")) {
               if (this.plugin.getConfig().getString("smelting.items." + items + ".material").equalsIgnoreCase("*")) {
                  limitations.setDisableAllItems(true);
               } else {
                  limitations.setItem(this.plugin.getConfig().getString("smelting.items." + items + ".material"));
               }

               this.getLimitations().add(limitations);
            }

         }
      });
   }

   public List<Limitations> getLimitations() {
      return this.limitations;
   }

   public boolean isCraftingEnabled() {
      return this.plugin.getConfig().getBoolean("crafting.enabled");
   }

   public boolean isSmeltingEnabled() {
      return this.plugin.getConfig().getBoolean("smelting.enabled");
   }

   public List<String> getCraftingItems() {
      ArrayList craftingLimitations = new ArrayList();

      try {
         this.getLimitations().forEach((craftingLimitation) -> {
            if (craftingLimitation.getType().equals(LimitationType.CRAFTING)) {
               craftingLimitations.add(craftingLimitation.getItem());
            }
         });
      } catch (Exception var3) {
         var3.printStackTrace();
      }

      return craftingLimitations;
   }

   public List<String> getSmeltingItems() {
      ArrayList smeltingLimitations = new ArrayList();

      try {
         this.getLimitations().forEach((smeltingLimitation) -> {
            if (smeltingLimitation.getType().equals(LimitationType.SMELTING)) {
               smeltingLimitations.add(smeltingLimitation.getItem());
            }
         });
      } catch (Exception var3) {
         var3.printStackTrace();
      }

      return smeltingLimitations;
   }
}
