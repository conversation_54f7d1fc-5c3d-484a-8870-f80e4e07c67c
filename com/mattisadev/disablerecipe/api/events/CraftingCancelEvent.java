package com.mattisadev.disablerecipe.api.events;

import com.mattisadev.disablerecipe.api.objects.Limitations;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

public class CraftingCancelEvent extends Event {
   private HandlerList handlers = new HandlerList();
   private Limitations limitation;
   private Player player;
   private boolean isCancelled;

   public CraftingCancelEvent(Limitations limitation, Player player) {
      this.limitation = limitation;
      this.player = player;
   }

   public boolean isCancelled() {
      return this.isCancelled;
   }

   public void setCancelled(boolean isCancelled) {
      this.isCancelled = isCancelled;
   }

   public HandlerList getHandlers() {
      return this.handlers;
   }

   public Limitations getLimitation() {
      return this.limitation;
   }

   public Player getPlayer() {
      return this.player;
   }
}
