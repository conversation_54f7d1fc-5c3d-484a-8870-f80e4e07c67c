package com.mattisadev.disablerecipe.api.objects;

import com.mattisadev.disablerecipe.api.enums.LimitationType;
import java.util.ArrayList;

public class Limitations {
   LimitationType type;
   ArrayList<String> world;
   String item;
   Boolean disableAllWorlds = false;
   Boolean disableAllItems = false;
   String bypassPermission = "";
   Boolean hasBypassPermission = false;

   public Limitations() {
   }

   public Limitations(String item) {
      this.item = item;
   }

   public LimitationType getType() {
      return this.type;
   }

   public void setType(LimitationType type) {
      this.type = type;
   }

   public ArrayList<String> getWorlds() {
      return this.world;
   }

   public void setWorlds(ArrayList<String> world) {
      this.world = world;
   }

   public String getItem() {
      return this.item;
   }

   public void setItem(String item) {
      this.item = item;
   }

   public Boolean disableAllWorlds() {
      return this.disableAllWorlds;
   }

   public void setDisableAllWorlds(Boolean disableAllWorlds) {
      this.disableAllWorlds = disableAllWorlds;
   }

   public Boolean disableAllItems() {
      return this.disableAllItems;
   }

   public void setDisableAllItems(Boolean disableAllItems) {
      this.disableAllItems = disableAllItems;
   }

   public String getBypassPermission() {
      return this.bypassPermission;
   }

   public void setBypassPermission(String bypassPermission) {
      this.bypassPermission = bypassPermission;
   }

   public Boolean hasBypassPermission() {
      return this.hasBypassPermission;
   }

   public void hasBypassPermission(Boolean value) {
      this.hasBypassPermission = value;
   }
}
