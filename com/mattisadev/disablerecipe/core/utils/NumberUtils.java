package com.mattisadev.disablerecipe.core.utils;

import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import jdk.internal.joptsimple.internal.Strings;
import org.apache.commons.lang.StringUtils;
import org.bukkit.Color;

public class NumberUtils {
   public static boolean isInt(String s) {
      try {
         Integer.parseInt(s);
         return true;
      } catch (NumberFormatException var2) {
         return false;
      }
   }

   public static boolean isBigInt(String s) {
      try {
         BigInteger.valueOf(Long.parseLong(s));
         return true;
      } catch (NumberFormatException var2) {
         return false;
      }
   }

   public static boolean isDouble(String s) {
      try {
         Double.parseDouble(s);
         return true;
      } catch (NumberFormatException var2) {
         return false;
      }
   }

   public static boolean isLong(String s) {
      try {
         Long.parseLong(s);
         return true;
      } catch (NumberFormatException var2) {
         return false;
      }
   }

   public static int randomInt(int min, int max) {
      return ThreadLocalRandom.current().nextInt(min, max + 1);
   }

   public static int randomInt(int max) {
      return ThreadLocalRandom.current().nextInt(max + 1);
   }

   public static double randomDouble(double min, double max) {
      return ThreadLocalRandom.current().nextDouble(min, max + 1.0D);
   }

   public static double randomDouble(double max) {
      return ThreadLocalRandom.current().nextDouble(max + 1.0D);
   }

   public static String convertCooldown(long time) {
      long seconds = time / 1000L;

      long minutes;
      for(minutes = 0L; seconds > 60L; ++minutes) {
         seconds -= 60L;
      }

      long hours;
      for(hours = 0L; minutes > 60L; ++hours) {
         minutes -= 60L;
      }

      String sHour = "";
      String sMinute = "";
      String sSecond = "";
      if (hours > 0L) {
         sHour = "" + hours + " hour(s) ";
      }

      if (minutes > 0L) {
         sMinute = "" + minutes + " minutes(s) ";
      }

      if (seconds > 0L) {
         sSecond = "" + seconds + " second(s)";
      }

      String message = sHour + sMinute + sSecond;
      return message.isEmpty() ? "now" : message;
   }

   @Nonnull
   public static Color parseColor(@Nullable String str) {
      if (Strings.isNullOrEmpty(str)) {
         return Color.BLACK;
      } else {
         String[] rgb = StringUtils.split(StringUtils.deleteWhitespace(str), ',');
         return rgb.length < 3 ? Color.WHITE : Color.fromRGB(org.apache.commons.lang.math.NumberUtils.toInt(rgb[0], 0), org.apache.commons.lang.math.NumberUtils.toInt(rgb[1], 0), org.apache.commons.lang.math.NumberUtils.toInt(rgb[2], 0));
      }
   }

   public static DecimalFormat decimalFormat(String format) {
      return new DecimalFormat(format);
   }

   public static int getPercentage(int max, double percentage) {
      return (int)((double)max * (percentage / 100.0D));
   }

   public String getRandomString(int length) {
      String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
      StringBuilder string = new StringBuilder();
      Random random = new Random();

      while(string.length() < length) {
         int index = (int)(random.nextFloat() * (float)characters.length());
         string.append(characters.charAt(index));
      }

      return string.toString();
   }
}
