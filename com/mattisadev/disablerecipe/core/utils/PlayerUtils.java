package com.mattisadev.disablerecipe.core.utils;

import com.mattisadev.disablerecipe.core.compatibility.ServerVersion;
import java.util.Map;
import java.util.UUID;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class PlayerUtils {
   public static UUID getOfflineUUID(String name) {
      UUID uuid = null;
      OfflinePlayer[] var2 = Bukkit.getOfflinePlayers();
      int var3 = var2.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         OfflinePlayer offlinePlayer = var2[var4];
         if (offlinePlayer.getName().equals(name)) {
            uuid = offlinePlayer.getUniqueId();
         }
      }

      return uuid;
   }

   public static void giveItem(Player player, ItemStack item, String message) {
      if (player != null && player.isOnline() && item != null) {
         Map<Integer, ItemStack> leftover = player.getInventory().addItem(new ItemStack[]{item});
         if (!leftover.isEmpty()) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
            leftover.values().stream().forEach((it) -> {
               player.getWorld().dropItemNaturally(player.getLocation(), it);
            });
         }

      }
   }

   public static ItemStack getHeldItem(Player player) {
      return ServerVersion.isServerVersionAbove(ServerVersion.V1_8) ? player.getInventory().getItemInMainHand() : player.getInventory().getItemInHand();
   }

   public static ItemStack getOffHeldItem(Player player) {
      return ServerVersion.isServerVersionAbove(ServerVersion.V1_8) ? player.getInventory().getItemInOffHand() : player.getInventory().getItemInHand();
   }
}
