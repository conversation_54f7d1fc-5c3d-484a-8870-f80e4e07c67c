<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.mattisadev</groupId>
  <artifactId>mcore</artifactId>
  <name>MCore</name>
  <version>2.4.1</version>
  <build>
    <sourceDirectory>./src/main/java</sourceDirectory>
    <defaultGoal>clean install</defaultGoal>
    <finalName>MCore-${project.version}</finalName>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.3.0</version>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <executions>
          <execution>
            <id>compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
          <execution>
            <id>testCompile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.2.1</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.0</version>
        <executions>
          <execution>
            <id>shaded</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <shadedArtifactAttached>false</shadedArtifactAttached>
              <createDependencyReducedPom>true</createDependencyReducedPom>
              <artifactSet>
                <includes>
                  <include>com.mattisadev:*</include>
                  <include>com.zaxxer:HikariCP</include>
                </includes>
              </artifactSet>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <includes>
                    <include>com/</include>
                    <include>META-INF/MANIFEST.MF</include>
                    <include>META-INF/maven/com.mattisadev/MCore/</include>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                    <exclude>META-INF/*.MF</exclude>
                  </includes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>jitpack.io</id>
      <url>https://jitpack.io</url>
    </repository>
    <repository>
      <id>placeholderapi</id>
      <url>https://repo.extendedclip.com/releases/</url>
    </repository>
    <repository>
      <id>minecraft-repo</id>
      <url>https://libraries.minecraft.net/</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.21.4-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.MilkBowl</groupId>
      <artifactId>VaultAPI</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>me.clip</groupId>
      <artifactId>placeholderapi</artifactId>
      <version>2.11.6</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.mojang</groupId>
      <artifactId>authlib</artifactId>
      <version>4.0.43</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.LoneDev6</groupId>
      <artifactId>api-itemsadder</artifactId>
      <version>3.5.0b</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <author>Matthew Wiggins</author>
  </properties>
</project>
