package com.mattisadev.mcore.compatibility;

import com.google.common.base.Enums;
import com.google.common.base.Strings;
import org.bukkit.Instrument;
import org.bukkit.Location;
import org.bukkit.Note;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <b>XSound</b> - Universal Minecraft Sound Support<br>
 * 1.13 and above as priority.
 * <p>
 * Sounds are thread-safe. But this doesn't mean you should
 * use a bukkit async scheduler for every {@link Player#playSound} call.
 * <p>
 * <b>Volume:</b> 0.0-∞ - 1.0f (normal) - Using higher values increase the distance from which the sound can be heard.<br>
 * <b>Pitch:</b> 0.5-2.0 - 1.0f (normal) - How fast the sound is play.
 * <p>
 * 1.8: <a href="http://docs.codelanx.com/Bukkit/1.8/org/bukkit/Sound.html">Sound Enum</a>
 * Latest: <a href="https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/Sound.html">Sound Enum</a>
 * Basics: <a href="https://bukkit.org/threads/151517/">Bukkit Thread</a>
 * play command: <a href="https://minecraft.gamepedia.com/Commands/play">minecraft.gamepedia.com</a>
 *
 * <AUTHOR> Morin
 * @version 9.2.0
 * @see Sound
 */
public enum XSound {
    AMBIENT_BASALT_DELTAS_ADDITIONS,
    AMBIENT_BASALT_DELTAS_LOOP,
    AMBIENT_BASALT_DELTAS_MOOD,
    AMBIENT_CAVE("AMBIENCE_CAVE"),
    AMBIENT_CRIMSON_FOREST_ADDITIONS,
    AMBIENT_CRIMSON_FOREST_LOOP,
    AMBIENT_CRIMSON_FOREST_MOOD,
    AMBIENT_NETHER_WASTES_ADDITIONS,
    AMBIENT_NETHER_WASTES_LOOP,
    AMBIENT_NETHER_WASTES_MOOD,
    AMBIENT_SOUL_SAND_VALLEY_ADDITIONS,
    AMBIENT_SOUL_SAND_VALLEY_LOOP,
    AMBIENT_SOUL_SAND_VALLEY_MOOD,
    AMBIENT_UNDERWATER_ENTER,
    AMBIENT_UNDERWATER_EXIT,
    AMBIENT_UNDERWATER_LOOP("AMBIENT_UNDERWATER_EXIT"),
    AMBIENT_UNDERWATER_LOOP_ADDITIONS("AMBIENT_UNDERWATER_EXIT"),
    AMBIENT_UNDERWATER_LOOP_ADDITIONS_RARE("AMBIENT_UNDERWATER_EXIT"),
    AMBIENT_UNDERWATER_LOOP_ADDITIONS_ULTRA_RARE("AMBIENT_UNDERWATER_EXIT"),
    AMBIENT_WARPED_FOREST_ADDITIONS,
    AMBIENT_WARPED_FOREST_LOOP,
    AMBIENT_WARPED_FOREST_MOOD,
    BLOCK_AMETHYST_BLOCK_BREAK,
    BLOCK_AMETHYST_BLOCK_CHIME,
    BLOCK_AMETHYST_BLOCK_FALL,
    BLOCK_AMETHYST_BLOCK_HIT,
    BLOCK_AMETHYST_BLOCK_PLACE,
    BLOCK_AMETHYST_BLOCK_RESONATE,
    BLOCK_AMETHYST_BLOCK_STEP,
    BLOCK_AMETHYST_CLUSTER_BREAK,
    BLOCK_AMETHYST_CLUSTER_FALL,
    BLOCK_AMETHYST_CLUSTER_HIT,
    BLOCK_AMETHYST_CLUSTER_PLACE,
    BLOCK_AMETHYST_CLUSTER_STEP,
    BLOCK_ANCIENT_DEBRIS_BREAK,
    BLOCK_ANCIENT_DEBRIS_FALL,
    BLOCK_ANCIENT_DEBRIS_HIT,
    BLOCK_ANCIENT_DEBRIS_PLACE,
    BLOCK_ANCIENT_DEBRIS_STEP,
    BLOCK_ANVIL_BREAK("ANVIL_BREAK"),
    BLOCK_ANVIL_DESTROY,
    BLOCK_ANVIL_FALL,
    BLOCK_ANVIL_HIT("BLOCK_ANVIL_FALL"),
    BLOCK_ANVIL_LAND("ANVIL_LAND"),
    BLOCK_ANVIL_PLACE("BLOCK_ANVIL_FALL"),
    BLOCK_ANVIL_STEP("BLOCK_ANVIL_FALL"),
    BLOCK_ANVIL_USE("ANVIL_USE"),
    BLOCK_AZALEA_BREAK,
    BLOCK_AZALEA_FALL,
    BLOCK_AZALEA_HIT,
    BLOCK_AZALEA_LEAVES_BREAK,
    BLOCK_AZALEA_LEAVES_FALL,
    BLOCK_AZALEA_LEAVES_HIT,
    BLOCK_AZALEA_LEAVES_PLACE,
    BLOCK_AZALEA_LEAVES_STEP,
    BLOCK_AZALEA_PLACE,
    BLOCK_AZALEA_STEP,
    BLOCK_BAMBOO_BREAK,
    BLOCK_BAMBOO_FALL,
    BLOCK_BAMBOO_HIT,
    BLOCK_BAMBOO_PLACE,
    BLOCK_BAMBOO_SAPLING_BREAK,
    BLOCK_BAMBOO_SAPLING_HIT,
    BLOCK_BAMBOO_SAPLING_PLACE,
    BLOCK_BAMBOO_STEP,
    BLOCK_BAMBOO_WOOD_BREAK,
    BLOCK_BAMBOO_WOOD_BUTTON_CLICK_OFF,
    BLOCK_BAMBOO_WOOD_BUTTON_CLICK_ON,
    BLOCK_BAMBOO_WOOD_DOOR_CLOSE,
    BLOCK_BAMBOO_WOOD_DOOR_OPEN,
    BLOCK_BAMBOO_WOOD_FALL,
    BLOCK_BAMBOO_WOOD_FENCE_GATE_CLOSE,
    BLOCK_BAMBOO_WOOD_FENCE_GATE_OPEN,
    BLOCK_BAMBOO_WOOD_HANGING_SIGN_BREAK,
    BLOCK_BAMBOO_WOOD_HANGING_SIGN_FALL,
    BLOCK_BAMBOO_WOOD_HANGING_SIGN_HIT,
    BLOCK_BAMBOO_WOOD_HANGING_SIGN_PLACE,
    BLOCK_BAMBOO_WOOD_HANGING_SIGN_STEP,
    BLOCK_BAMBOO_WOOD_HIT,
    BLOCK_BAMBOO_WOOD_PLACE,
    BLOCK_BAMBOO_WOOD_PRESSURE_PLATE_CLICK_OFF,
    BLOCK_BAMBOO_WOOD_PRESSURE_PLATE_CLICK_ON,
    BLOCK_BAMBOO_WOOD_STEP,
    BLOCK_BAMBOO_WOOD_TRAPDOOR_CLOSE,
    BLOCK_BAMBOO_WOOD_TRAPDOOR_OPEN,
    BLOCK_BARREL_CLOSE,
    BLOCK_BARREL_OPEN,
    BLOCK_BASALT_BREAK,
    BLOCK_BASALT_FALL,
    BLOCK_BASALT_HIT,
    BLOCK_BASALT_PLACE,
    BLOCK_BASALT_STEP,
    BLOCK_BEACON_ACTIVATE,
    BLOCK_BEACON_AMBIENT,
    BLOCK_BEACON_DEACTIVATE("BLOCK_BEACON_AMBIENT"),
    BLOCK_BEACON_POWER_SELECT("BLOCK_BEACON_AMBIENT"),
    BLOCK_BEEHIVE_DRIP,
    BLOCK_BEEHIVE_ENTER,
    BLOCK_BEEHIVE_EXIT,
    BLOCK_BEEHIVE_SHEAR,
    BLOCK_BEEHIVE_WORK,
    BLOCK_BELL_RESONATE,
    BLOCK_BELL_USE,
    BLOCK_BIG_DRIPLEAF_BREAK,
    BLOCK_BIG_DRIPLEAF_FALL,
    BLOCK_BIG_DRIPLEAF_HIT,
    BLOCK_BIG_DRIPLEAF_PLACE,
    BLOCK_BIG_DRIPLEAF_STEP,
    BLOCK_BIG_DRIPLEAF_TILT_DOWN,
    BLOCK_BIG_DRIPLEAF_TILT_UP,
    BLOCK_BLASTFURNACE_FIRE_CRACKLE,
    BLOCK_BONE_BLOCK_BREAK,
    BLOCK_BONE_BLOCK_FALL,
    BLOCK_BONE_BLOCK_HIT,
    BLOCK_BONE_BLOCK_PLACE,
    BLOCK_BONE_BLOCK_STEP,
    BLOCK_BREWING_STAND_BREW,
    BLOCK_BUBBLE_COLUMN_BUBBLE_POP,
    BLOCK_BUBBLE_COLUMN_UPWARDS_AMBIENT,
    BLOCK_BUBBLE_COLUMN_UPWARDS_INSIDE,
    BLOCK_BUBBLE_COLUMN_WHIRLPOOL_AMBIENT,
    BLOCK_BUBBLE_COLUMN_WHIRLPOOL_INSIDE,
    BLOCK_CAKE_ADD_CANDLE,
    BLOCK_CALCITE_BREAK,
    BLOCK_CALCITE_FALL,
    BLOCK_CALCITE_HIT,
    BLOCK_CALCITE_PLACE,
    BLOCK_CALCITE_STEP,
    BLOCK_CAMPFIRE_CRACKLE,
    BLOCK_CANDLE_AMBIENT,
    BLOCK_CANDLE_BREAK,
    BLOCK_CANDLE_EXTINGUISH,
    BLOCK_CANDLE_FALL,
    BLOCK_CANDLE_HIT,
    BLOCK_CANDLE_PLACE,
    BLOCK_CANDLE_STEP,
    BLOCK_CAVE_VINES_BREAK,
    BLOCK_CAVE_VINES_FALL,
    BLOCK_CAVE_VINES_HIT,
    BLOCK_CAVE_VINES_PICK_BERRIES,
    BLOCK_CAVE_VINES_PLACE,
    BLOCK_CAVE_VINES_STEP,
    BLOCK_CHAIN_BREAK,
    BLOCK_CHAIN_FALL,
    BLOCK_CHAIN_HIT,
    BLOCK_CHAIN_PLACE,
    BLOCK_CHAIN_STEP,
    BLOCK_CHERRY_LEAVES_BREAK,
    BLOCK_CHERRY_LEAVES_FALL,
    BLOCK_CHERRY_LEAVES_HIT,
    BLOCK_CHERRY_LEAVES_PLACE,
    BLOCK_CHERRY_LEAVES_STEP,
    BLOCK_CHERRY_SAPLING_BREAK,
    BLOCK_CHERRY_SAPLING_FALL,
    BLOCK_CHERRY_SAPLING_HIT,
    BLOCK_CHERRY_SAPLING_PLACE,
    BLOCK_CHERRY_SAPLING_STEP,
    BLOCK_CHERRY_WOOD_BREAK,
    BLOCK_CHERRY_WOOD_BUTTON_CLICK_OFF,
    BLOCK_CHERRY_WOOD_BUTTON_CLICK_ON,
    BLOCK_CHERRY_WOOD_DOOR_CLOSE,
    BLOCK_CHERRY_WOOD_DOOR_OPEN,
    BLOCK_CHERRY_WOOD_FALL,
    BLOCK_CHERRY_WOOD_FENCE_GATE_CLOSE,
    BLOCK_CHERRY_WOOD_FENCE_GATE_OPEN,
    BLOCK_CHERRY_WOOD_HANGING_SIGN_BREAK,
    BLOCK_CHERRY_WOOD_HANGING_SIGN_FALL,
    BLOCK_CHERRY_WOOD_HANGING_SIGN_HIT,
    BLOCK_CHERRY_WOOD_HANGING_SIGN_PLACE,
    BLOCK_CHERRY_WOOD_HANGING_SIGN_STEP,
    BLOCK_CHERRY_WOOD_HIT,
    BLOCK_CHERRY_WOOD_PLACE,
    BLOCK_CHERRY_WOOD_PRESSURE_PLATE_CLICK_OFF,
    BLOCK_CHERRY_WOOD_PRESSURE_PLATE_CLICK_ON,
    BLOCK_CHERRY_WOOD_STEP,
    BLOCK_CHERRY_WOOD_TRAPDOOR_CLOSE,
    BLOCK_CHERRY_WOOD_TRAPDOOR_OPEN,
    BLOCK_CHEST_CLOSE("CHEST_CLOSE", "ENTITY_CHEST_CLOSE"),
    BLOCK_CHEST_LOCKED,
    BLOCK_CHEST_OPEN("CHEST_OPEN", "ENTITY_CHEST_OPEN"),
    BLOCK_CHISELED_BOOKSHELF_BREAK,
    BLOCK_CHISELED_BOOKSHELF_FALL,
    BLOCK_CHISELED_BOOKSHELF_HIT,
    BLOCK_CHISELED_BOOKSHELF_INSERT,
    BLOCK_CHISELED_BOOKSHELF_INSERT_ENCHANTED,
    BLOCK_CHISELED_BOOKSHELF_PICKUP,
    BLOCK_CHISELED_BOOKSHELF_PICKUP_ENCHANTED,
    BLOCK_CHISELED_BOOKSHELF_PLACE,
    BLOCK_CHISELED_BOOKSHELF_STEP,
    BLOCK_CHORUS_FLOWER_DEATH,
    BLOCK_CHORUS_FLOWER_GROW,
    BLOCK_COMPARATOR_CLICK,
    BLOCK_COMPOSTER_EMPTY,
    BLOCK_COMPOSTER_FILL,
    BLOCK_COMPOSTER_FILL_SUCCESS,
    BLOCK_COMPOSTER_READY,
    BLOCK_CONDUIT_ACTIVATE,
    BLOCK_CONDUIT_AMBIENT,
    BLOCK_CONDUIT_AMBIENT_SHORT,
    BLOCK_CONDUIT_ATTACK_TARGET,
    BLOCK_CONDUIT_DEACTIVATE,
    BLOCK_COPPER_BREAK,
    BLOCK_COPPER_FALL,
    BLOCK_COPPER_HIT,
    BLOCK_COPPER_PLACE,
    BLOCK_COPPER_STEP,
    BLOCK_CORAL_BLOCK_BREAK,
    BLOCK_CORAL_BLOCK_FALL,
    BLOCK_CORAL_BLOCK_HIT,
    BLOCK_CORAL_BLOCK_PLACE,
    BLOCK_CORAL_BLOCK_STEP,
    BLOCK_CROP_BREAK,
    BLOCK_DECORATED_POT_BREAK,
    BLOCK_DECORATED_POT_FALL,
    BLOCK_DECORATED_POT_HIT,
    BLOCK_DECORATED_POT_PLACE,
    BLOCK_DECORATED_POT_SHATTER,
    BLOCK_DECORATED_POT_STEP,
    BLOCK_DEEPSLATE_BREAK,
    BLOCK_DEEPSLATE_BRICKS_BREAK,
    BLOCK_DEEPSLATE_BRICKS_FALL,
    BLOCK_DEEPSLATE_BRICKS_HIT,
    BLOCK_DEEPSLATE_BRICKS_PLACE,
    BLOCK_DEEPSLATE_BRICKS_STEP,
    BLOCK_DEEPSLATE_FALL,
    BLOCK_DEEPSLATE_HIT,
    BLOCK_DEEPSLATE_PLACE,
    BLOCK_DEEPSLATE_STEP,
    BLOCK_DEEPSLATE_TILES_BREAK,
    BLOCK_DEEPSLATE_TILES_FALL,
    BLOCK_DEEPSLATE_TILES_HIT,
    BLOCK_DEEPSLATE_TILES_PLACE,
    BLOCK_DEEPSLATE_TILES_STEP,
    BLOCK_DISPENSER_DISPENSE,
    BLOCK_DISPENSER_FAIL,
    BLOCK_DISPENSER_LAUNCH,
    BLOCK_DRIPSTONE_BLOCK_BREAK,
    BLOCK_DRIPSTONE_BLOCK_FALL,
    BLOCK_DRIPSTONE_BLOCK_HIT,
    BLOCK_DRIPSTONE_BLOCK_PLACE,
    BLOCK_DRIPSTONE_BLOCK_STEP,
    BLOCK_ENCHANTMENT_TABLE_USE,
    BLOCK_ENDER_CHEST_CLOSE,
    BLOCK_ENDER_CHEST_OPEN,
    BLOCK_END_GATEWAY_SPAWN,
    BLOCK_END_PORTAL_FRAME_FILL,
    BLOCK_END_PORTAL_SPAWN,
    BLOCK_FENCE_GATE_CLOSE,
    BLOCK_FENCE_GATE_OPEN,
    BLOCK_FIRE_AMBIENT("FIRE"),
    BLOCK_FIRE_EXTINGUISH("FIZZ"),
    BLOCK_FLOWERING_AZALEA_BREAK,
    BLOCK_FLOWERING_AZALEA_FALL,
    BLOCK_FLOWERING_AZALEA_HIT,
    BLOCK_FLOWERING_AZALEA_PLACE,
    BLOCK_FLOWERING_AZALEA_STEP,
    BLOCK_FROGLIGHT_BREAK,
    BLOCK_FROGLIGHT_FALL,
    BLOCK_FROGLIGHT_HIT,
    BLOCK_FROGLIGHT_PLACE,
    BLOCK_FROGLIGHT_STEP,
    BLOCK_FROGSPAWN_BREAK,
    BLOCK_FROGSPAWN_FALL,
    BLOCK_FROGSPAWN_HATCH,
    BLOCK_FROGSPAWN_HIT,
    BLOCK_FROGSPAWN_PLACE,
    BLOCK_FROGSPAWN_STEP,
    BLOCK_FUNGUS_BREAK,
    BLOCK_FUNGUS_FALL,
    BLOCK_FUNGUS_HIT,
    BLOCK_FUNGUS_PLACE,
    BLOCK_FUNGUS_STEP,
    BLOCK_FURNACE_FIRE_CRACKLE,
    BLOCK_GILDED_BLACKSTONE_BREAK,
    BLOCK_GILDED_BLACKSTONE_FALL,
    BLOCK_GILDED_BLACKSTONE_HIT,
    BLOCK_GILDED_BLACKSTONE_PLACE,
    BLOCK_GILDED_BLACKSTONE_STEP,
    BLOCK_GLASS_BREAK("GLASS"),
    BLOCK_GLASS_FALL,
    BLOCK_GLASS_HIT,
    BLOCK_GLASS_PLACE,
    BLOCK_GLASS_STEP,
    BLOCK_GRASS_BREAK("DIG_GRASS"),
    BLOCK_GRASS_FALL,
    BLOCK_GRASS_HIT,
    BLOCK_GRASS_PLACE,
    BLOCK_GRASS_STEP("STEP_GRASS"),
    BLOCK_GRAVEL_BREAK("DIG_GRAVEL"),
    BLOCK_GRAVEL_FALL,
    BLOCK_GRAVEL_HIT,
    BLOCK_GRAVEL_PLACE,
    BLOCK_GRAVEL_STEP("STEP_GRAVEL"),
    BLOCK_GRINDSTONE_USE,
    BLOCK_GROWING_PLANT_CROP,
    BLOCK_HANGING_ROOTS_BREAK,
    BLOCK_HANGING_ROOTS_FALL,
    BLOCK_HANGING_ROOTS_HIT,
    BLOCK_HANGING_ROOTS_PLACE,
    BLOCK_HANGING_ROOTS_STEP,
    BLOCK_HANGING_SIGN_BREAK,
    BLOCK_HANGING_SIGN_FALL,
    BLOCK_HANGING_SIGN_HIT,
    BLOCK_HANGING_SIGN_PLACE,
    BLOCK_HANGING_SIGN_STEP,
    BLOCK_HONEY_BLOCK_BREAK,
    BLOCK_HONEY_BLOCK_FALL,
    BLOCK_HONEY_BLOCK_HIT,
    BLOCK_HONEY_BLOCK_PLACE,
    BLOCK_HONEY_BLOCK_SLIDE,
    BLOCK_HONEY_BLOCK_STEP,
    BLOCK_IRON_DOOR_CLOSE,
    BLOCK_IRON_DOOR_OPEN,
    BLOCK_IRON_TRAPDOOR_CLOSE,
    BLOCK_IRON_TRAPDOOR_OPEN,
    BLOCK_LADDER_BREAK,
    BLOCK_LADDER_FALL,
    BLOCK_LADDER_HIT,
    BLOCK_LADDER_PLACE,
    BLOCK_LADDER_STEP("STEP_LADDER"),
    BLOCK_LANTERN_BREAK,
    BLOCK_LANTERN_FALL,
    BLOCK_LANTERN_HIT,
    BLOCK_LANTERN_PLACE,
    BLOCK_LANTERN_STEP,
    BLOCK_LARGE_AMETHYST_BUD_BREAK,
    BLOCK_LARGE_AMETHYST_BUD_PLACE,
    BLOCK_LAVA_AMBIENT("LAVA"),
    BLOCK_LAVA_EXTINGUISH,
    BLOCK_LAVA_POP("LAVA_POP"),
    BLOCK_LEVER_CLICK,
    BLOCK_LILY_PAD_PLACE("BLOCK_WATERLILY_PLACE"),
    BLOCK_LODESTONE_BREAK,
    BLOCK_LODESTONE_FALL,
    BLOCK_LODESTONE_HIT,
    BLOCK_LODESTONE_PLACE,
    BLOCK_LODESTONE_STEP,
    BLOCK_MANGROVE_ROOTS_BREAK,
    BLOCK_MANGROVE_ROOTS_FALL,
    BLOCK_MANGROVE_ROOTS_HIT,
    BLOCK_MANGROVE_ROOTS_PLACE,
    BLOCK_MANGROVE_ROOTS_STEP,
    BLOCK_MEDIUM_AMETHYST_BUD_BREAK,
    BLOCK_MEDIUM_AMETHYST_BUD_PLACE,
    BLOCK_METAL_BREAK,
    BLOCK_METAL_FALL,
    BLOCK_METAL_HIT,
    BLOCK_METAL_PLACE,
    BLOCK_METAL_PRESSURE_PLATE_CLICK_OFF("BLOCK_METAL_PRESSUREPLATE_CLICK_OFF"),
    BLOCK_METAL_PRESSURE_PLATE_CLICK_ON("BLOCK_METAL_PRESSUREPLATE_CLICK_ON"),
    BLOCK_METAL_STEP,
    BLOCK_MOSS_BREAK,
    BLOCK_MOSS_CARPET_BREAK,
    BLOCK_MOSS_CARPET_FALL,
    BLOCK_MOSS_CARPET_HIT,
    BLOCK_MOSS_CARPET_PLACE,
    BLOCK_MOSS_CARPET_STEP,
    BLOCK_MOSS_FALL,
    BLOCK_MOSS_HIT,
    BLOCK_MOSS_PLACE,
    BLOCK_MOSS_STEP,
    BLOCK_MUDDY_MANGROVE_ROOTS_BREAK,
    BLOCK_MUDDY_MANGROVE_ROOTS_FALL,
    BLOCK_MUDDY_MANGROVE_ROOTS_HIT,
    BLOCK_MUDDY_MANGROVE_ROOTS_PLACE,
    BLOCK_MUDDY_MANGROVE_ROOTS_STEP,
    BLOCK_MUD_BREAK,
    BLOCK_MUD_BRICKS_BREAK,
    BLOCK_MUD_BRICKS_FALL,
    BLOCK_MUD_BRICKS_HIT,
    BLOCK_MUD_BRICKS_PLACE,
    BLOCK_MUD_BRICKS_STEP,
    BLOCK_MUD_FALL,
    BLOCK_MUD_HIT,
    BLOCK_MUD_PLACE,
    BLOCK_MUD_STEP,
    BLOCK_NETHERITE_BLOCK_BREAK,
    BLOCK_NETHERITE_BLOCK_FALL,
    BLOCK_NETHERITE_BLOCK_HIT,
    BLOCK_NETHERITE_BLOCK_PLACE,
    BLOCK_NETHERITE_BLOCK_STEP,
    BLOCK_NETHERRACK_BREAK,
    BLOCK_NETHERRACK_FALL,
    BLOCK_NETHERRACK_HIT,
    BLOCK_NETHERRACK_PLACE,
    BLOCK_NETHERRACK_STEP,
    BLOCK_NETHER_BRICKS_BREAK,
    BLOCK_NETHER_BRICKS_FALL,
    BLOCK_NETHER_BRICKS_HIT,
    BLOCK_NETHER_BRICKS_PLACE,
    BLOCK_NETHER_BRICKS_STEP,
    BLOCK_NETHER_GOLD_ORE_BREAK,
    BLOCK_NETHER_GOLD_ORE_FALL,
    BLOCK_NETHER_GOLD_ORE_HIT,
    BLOCK_NETHER_GOLD_ORE_PLACE,
    BLOCK_NETHER_GOLD_ORE_STEP,
    BLOCK_NETHER_ORE_BREAK,
    BLOCK_NETHER_ORE_FALL,
    BLOCK_NETHER_ORE_HIT,
    BLOCK_NETHER_ORE_PLACE,
    BLOCK_NETHER_ORE_STEP,
    BLOCK_NETHER_SPROUTS_BREAK,
    BLOCK_NETHER_SPROUTS_FALL,
    BLOCK_NETHER_SPROUTS_HIT,
    BLOCK_NETHER_SPROUTS_PLACE,
    BLOCK_NETHER_SPROUTS_STEP,
    BLOCK_NETHER_WART_BREAK,
    BLOCK_NETHER_WOOD_BREAK,
    BLOCK_NETHER_WOOD_BUTTON_CLICK_OFF,
    BLOCK_NETHER_WOOD_BUTTON_CLICK_ON,
    BLOCK_NETHER_WOOD_DOOR_CLOSE,
    BLOCK_NETHER_WOOD_DOOR_OPEN,
    BLOCK_NETHER_WOOD_FALL,
    BLOCK_NETHER_WOOD_FENCE_GATE_CLOSE,
    BLOCK_NETHER_WOOD_FENCE_GATE_OPEN,
    BLOCK_NETHER_WOOD_HANGING_SIGN_BREAK,
    BLOCK_NETHER_WOOD_HANGING_SIGN_FALL,
    BLOCK_NETHER_WOOD_HANGING_SIGN_HIT,
    BLOCK_NETHER_WOOD_HANGING_SIGN_PLACE,
    BLOCK_NETHER_WOOD_HANGING_SIGN_STEP,
    BLOCK_NETHER_WOOD_HIT,
    BLOCK_NETHER_WOOD_PLACE,
    BLOCK_NETHER_WOOD_PRESSURE_PLATE_CLICK_OFF,
    BLOCK_NETHER_WOOD_PRESSURE_PLATE_CLICK_ON,
    BLOCK_NETHER_WOOD_STEP,
    BLOCK_NETHER_WOOD_TRAPDOOR_CLOSE,
    BLOCK_NETHER_WOOD_TRAPDOOR_OPEN,
    BLOCK_NOTE_BLOCK_BANJO,
    BLOCK_NOTE_BLOCK_BASEDRUM("NOTE_BASS_DRUM", "BLOCK_NOTE_BASEDRUM"),
    BLOCK_NOTE_BLOCK_BASS("NOTE_BASS", "BLOCK_NOTE_BASS"),
    BLOCK_NOTE_BLOCK_BELL("BLOCK_NOTE_BELL"),
    BLOCK_NOTE_BLOCK_BIT,
    BLOCK_NOTE_BLOCK_CHIME("BLOCK_NOTE_CHIME"),
    BLOCK_NOTE_BLOCK_COW_BELL,
    BLOCK_NOTE_BLOCK_DIDGERIDOO,
    BLOCK_NOTE_BLOCK_FLUTE("BLOCK_NOTE_FLUTE"),
    BLOCK_NOTE_BLOCK_GUITAR("NOTE_BASS_GUITAR", "BLOCK_NOTE_GUITAR"),
    BLOCK_NOTE_BLOCK_HARP("NOTE_PIANO", "BLOCK_NOTE_HARP"),
    BLOCK_NOTE_BLOCK_HAT("NOTE_STICKS", "BLOCK_NOTE_HAT"),
    BLOCK_NOTE_BLOCK_IMITATE_CREEPER,
    BLOCK_NOTE_BLOCK_IMITATE_ENDER_DRAGON,
    BLOCK_NOTE_BLOCK_IMITATE_PIGLIN,
    BLOCK_NOTE_BLOCK_IMITATE_SKELETON,
    BLOCK_NOTE_BLOCK_IMITATE_WITHER_SKELETON,
    BLOCK_NOTE_BLOCK_IMITATE_ZOMBIE,
    BLOCK_NOTE_BLOCK_IRON_XYLOPHONE,
    BLOCK_NOTE_BLOCK_PLING("NOTE_PLING", "BLOCK_NOTE_PLING"),
    BLOCK_NOTE_BLOCK_SNARE("NOTE_SNARE_DRUM", "BLOCK_NOTE_SNARE"),
    BLOCK_NOTE_BLOCK_XYLOPHONE("BLOCK_NOTE_XYLOPHONE"),
    BLOCK_NYLIUM_BREAK,
    BLOCK_NYLIUM_FALL,
    BLOCK_NYLIUM_HIT,
    BLOCK_NYLIUM_PLACE,
    BLOCK_NYLIUM_STEP,
    BLOCK_PACKED_MUD_BREAK,
    BLOCK_PACKED_MUD_FALL,
    BLOCK_PACKED_MUD_HIT,
    BLOCK_PACKED_MUD_PLACE,
    BLOCK_PACKED_MUD_STEP,
    BLOCK_PINK_PETALS_BREAK,
    BLOCK_PINK_PETALS_FALL,
    BLOCK_PINK_PETALS_HIT,
    BLOCK_PINK_PETALS_PLACE,
    BLOCK_PINK_PETALS_STEP,
    BLOCK_PISTON_CONTRACT("PISTON_RETRACT"),
    BLOCK_PISTON_EXTEND("PISTON_EXTEND"),
    BLOCK_POINTED_DRIPSTONE_BREAK,
    BLOCK_POINTED_DRIPSTONE_DRIP_LAVA,
    BLOCK_POINTED_DRIPSTONE_DRIP_LAVA_INTO_CAULDRON,
    BLOCK_POINTED_DRIPSTONE_DRIP_WATER,
    BLOCK_POINTED_DRIPSTONE_DRIP_WATER_INTO_CAULDRON,
    BLOCK_POINTED_DRIPSTONE_FALL,
    BLOCK_POINTED_DRIPSTONE_HIT,
    BLOCK_POINTED_DRIPSTONE_LAND,
    BLOCK_POINTED_DRIPSTONE_PLACE,
    BLOCK_POINTED_DRIPSTONE_STEP,
    BLOCK_POLISHED_DEEPSLATE_BREAK,
    BLOCK_POLISHED_DEEPSLATE_FALL,
    BLOCK_POLISHED_DEEPSLATE_HIT,
    BLOCK_POLISHED_DEEPSLATE_PLACE,
    BLOCK_POLISHED_DEEPSLATE_STEP,
    BLOCK_PORTAL_AMBIENT("PORTAL"),
    BLOCK_PORTAL_TRAVEL("PORTAL_TRAVEL"),
    BLOCK_PORTAL_TRIGGER("PORTAL_TRIGGER"),
    BLOCK_POWDER_SNOW_BREAK,
    BLOCK_POWDER_SNOW_FALL,
    BLOCK_POWDER_SNOW_HIT,
    BLOCK_POWDER_SNOW_PLACE,
    BLOCK_POWDER_SNOW_STEP,
    BLOCK_PUMPKIN_CARVE,
    BLOCK_REDSTONE_TORCH_BURNOUT,
    BLOCK_RESPAWN_ANCHOR_AMBIENT,
    BLOCK_RESPAWN_ANCHOR_CHARGE,
    BLOCK_RESPAWN_ANCHOR_DEPLETE,
    BLOCK_RESPAWN_ANCHOR_SET_SPAWN,
    BLOCK_ROOTED_DIRT_BREAK,
    BLOCK_ROOTED_DIRT_FALL,
    BLOCK_ROOTED_DIRT_HIT,
    BLOCK_ROOTED_DIRT_PLACE,
    BLOCK_ROOTED_DIRT_STEP,
    BLOCK_ROOTS_BREAK,
    BLOCK_ROOTS_FALL,
    BLOCK_ROOTS_HIT,
    BLOCK_ROOTS_PLACE,
    BLOCK_ROOTS_STEP,
    BLOCK_SAND_BREAK("DIG_SAND"),
    BLOCK_SAND_FALL,
    BLOCK_SAND_HIT,
    BLOCK_SAND_PLACE,
    BLOCK_SAND_STEP("STEP_SAND"),
    BLOCK_SCAFFOLDING_BREAK,
    BLOCK_SCAFFOLDING_FALL,
    BLOCK_SCAFFOLDING_HIT,
    BLOCK_SCAFFOLDING_PLACE,
    BLOCK_SCAFFOLDING_STEP,
    BLOCK_SCULK_BREAK,
    BLOCK_SCULK_CATALYST_BLOOM,
    BLOCK_SCULK_CATALYST_BREAK,
    BLOCK_SCULK_CATALYST_FALL,
    BLOCK_SCULK_CATALYST_HIT,
    BLOCK_SCULK_CATALYST_PLACE,
    BLOCK_SCULK_CATALYST_STEP,
    BLOCK_SCULK_CHARGE,
    BLOCK_SCULK_FALL,
    BLOCK_SCULK_HIT,
    BLOCK_SCULK_PLACE,
    BLOCK_SCULK_SENSOR_BREAK,
    BLOCK_SCULK_SENSOR_CLICKING,
    BLOCK_SCULK_SENSOR_CLICKING_STOP,
    BLOCK_SCULK_SENSOR_FALL,
    BLOCK_SCULK_SENSOR_HIT,
    BLOCK_SCULK_SENSOR_PLACE,
    BLOCK_SCULK_SENSOR_STEP,
    BLOCK_SCULK_SHRIEKER_BREAK,
    BLOCK_SCULK_SHRIEKER_FALL,
    BLOCK_SCULK_SHRIEKER_HIT,
    BLOCK_SCULK_SHRIEKER_PLACE,
    BLOCK_SCULK_SHRIEKER_SHRIEK,
    BLOCK_SCULK_SHRIEKER_STEP,
    BLOCK_SCULK_SPREAD,
    BLOCK_SCULK_STEP,
    BLOCK_SCULK_VEIN_BREAK,
    BLOCK_SCULK_VEIN_FALL,
    BLOCK_SCULK_VEIN_HIT,
    BLOCK_SCULK_VEIN_PLACE,
    BLOCK_SCULK_VEIN_STEP,
    BLOCK_SHROOMLIGHT_BREAK,
    BLOCK_SHROOMLIGHT_FALL,
    BLOCK_SHROOMLIGHT_HIT,
    BLOCK_SHROOMLIGHT_PLACE,
    BLOCK_SHROOMLIGHT_STEP,
    BLOCK_SHULKER_BOX_CLOSE,
    BLOCK_SHULKER_BOX_OPEN,
    BLOCK_SIGN_WAXED_INTERACT_FAIL,
    BLOCK_SLIME_BLOCK_BREAK("BLOCK_SLIME_BREAK"),
    BLOCK_SLIME_BLOCK_FALL("BLOCK_SLIME_FALL"),
    BLOCK_SLIME_BLOCK_HIT("BLOCK_SLIME_HIT"),
    BLOCK_SLIME_BLOCK_PLACE("BLOCK_SLIME_PLACE"),
    BLOCK_SLIME_BLOCK_STEP("BLOCK_SLIME_STEP"),
    BLOCK_SMALL_AMETHYST_BUD_BREAK,
    BLOCK_SMALL_AMETHYST_BUD_PLACE,
    BLOCK_SMALL_DRIPLEAF_BREAK,
    BLOCK_SMALL_DRIPLEAF_FALL,
    BLOCK_SMALL_DRIPLEAF_HIT,
    BLOCK_SMALL_DRIPLEAF_PLACE,
    BLOCK_SMALL_DRIPLEAF_STEP,
    BLOCK_SMITHING_TABLE_USE,
    BLOCK_SMOKER_SMOKE,
    BLOCK_SNIFFER_EGG_CRACK,
    BLOCK_SNIFFER_EGG_HATCH,
    BLOCK_SNIFFER_EGG_PLOP,
    BLOCK_SNOW_BREAK("DIG_SNOW"),
    BLOCK_SNOW_FALL,
    BLOCK_SNOW_HIT,
    BLOCK_SNOW_PLACE,
    BLOCK_SNOW_STEP("STEP_SNOW"),
    BLOCK_SOUL_SAND_BREAK,
    BLOCK_SOUL_SAND_FALL,
    BLOCK_SOUL_SAND_HIT,
    BLOCK_SOUL_SAND_PLACE,
    BLOCK_SOUL_SAND_STEP,
    BLOCK_SOUL_SOIL_BREAK,
    BLOCK_SOUL_SOIL_FALL,
    BLOCK_SOUL_SOIL_HIT,
    BLOCK_SOUL_SOIL_PLACE,
    BLOCK_SOUL_SOIL_STEP,
    BLOCK_SPORE_BLOSSOM_BREAK,
    BLOCK_SPORE_BLOSSOM_FALL,
    BLOCK_SPORE_BLOSSOM_HIT,
    BLOCK_SPORE_BLOSSOM_PLACE,
    BLOCK_SPORE_BLOSSOM_STEP,
    BLOCK_STEM_BREAK,
    BLOCK_STEM_FALL,
    BLOCK_STEM_HIT,
    BLOCK_STEM_PLACE,
    BLOCK_STEM_STEP,
    BLOCK_STONE_BREAK("DIG_STONE"),
    BLOCK_STONE_BUTTON_CLICK_OFF,
    BLOCK_STONE_BUTTON_CLICK_ON,
    BLOCK_STONE_FALL,
    BLOCK_STONE_HIT,
    BLOCK_STONE_PLACE,
    BLOCK_STONE_PRESSURE_PLATE_CLICK_OFF("BLOCK_STONE_PRESSUREPLATE_CLICK_OFF"),
    BLOCK_STONE_PRESSURE_PLATE_CLICK_ON("BLOCK_STONE_PRESSUREPLATE_CLICK_ON"),
    BLOCK_STONE_STEP("STEP_STONE"),
    BLOCK_SUSPICIOUS_GRAVEL_BREAK,
    BLOCK_SUSPICIOUS_GRAVEL_FALL,
    BLOCK_SUSPICIOUS_GRAVEL_HIT,
    BLOCK_SUSPICIOUS_GRAVEL_PLACE,
    BLOCK_SUSPICIOUS_GRAVEL_STEP,
    BLOCK_SUSPICIOUS_SAND_BREAK,
    BLOCK_SUSPICIOUS_SAND_FALL,
    BLOCK_SUSPICIOUS_SAND_HIT,
    BLOCK_SUSPICIOUS_SAND_PLACE,
    BLOCK_SUSPICIOUS_SAND_STEP,
    BLOCK_SWEET_BERRY_BUSH_BREAK,
    BLOCK_SWEET_BERRY_BUSH_PICK_BERRIES("ITEM_SWEET_BERRIES_PICK_FROM_BUSH"),
    BLOCK_SWEET_BERRY_BUSH_PLACE,
    BLOCK_TRIPWIRE_ATTACH,
    BLOCK_TRIPWIRE_CLICK_OFF,
    BLOCK_TRIPWIRE_CLICK_ON,
    BLOCK_TRIPWIRE_DETACH,
    BLOCK_TUFF_BREAK,
    BLOCK_TUFF_FALL,
    BLOCK_TUFF_HIT,
    BLOCK_TUFF_PLACE,
    BLOCK_TUFF_STEP,
    BLOCK_VINE_BREAK,
    BLOCK_VINE_FALL,
    BLOCK_VINE_HIT,
    BLOCK_VINE_PLACE,
    BLOCK_VINE_STEP,
    BLOCK_WART_BLOCK_BREAK,
    BLOCK_WART_BLOCK_FALL,
    BLOCK_WART_BLOCK_HIT,
    BLOCK_WART_BLOCK_PLACE,
    BLOCK_WART_BLOCK_STEP,
    BLOCK_WATER_AMBIENT("WATER"),
    BLOCK_WEEPING_VINES_BREAK,
    BLOCK_WEEPING_VINES_FALL,
    BLOCK_WEEPING_VINES_HIT,
    BLOCK_WEEPING_VINES_PLACE,
    BLOCK_WEEPING_VINES_STEP,
    BLOCK_WET_GRASS_BREAK,
    BLOCK_WET_GRASS_FALL,
    BLOCK_WET_GRASS_HIT,
    BLOCK_WET_GRASS_PLACE("BLOCK_WET_GRASS_HIT"),
    BLOCK_WET_GRASS_STEP("BLOCK_WET_GRASS_HIT"),
    BLOCK_WOODEN_BUTTON_CLICK_OFF("WOOD_CLICK", "BLOCK_WOOD_BUTTON_CLICK_OFF"),
    BLOCK_WOODEN_BUTTON_CLICK_ON("WOOD_CLICK", "BLOCK_WOOD_BUTTON_CLICK_ON"),
    BLOCK_WOODEN_DOOR_CLOSE("DOOR_CLOSE"),
    BLOCK_WOODEN_DOOR_OPEN("DOOR_OPEN"),
    BLOCK_WOODEN_PRESSURE_PLATE_CLICK_OFF("BLOCK_WOOD_PRESSUREPLATE_CLICK_OFF"),
    BLOCK_WOODEN_PRESSURE_PLATE_CLICK_ON("BLOCK_WOOD_PRESSUREPLATE_CLICK_ON"),
    BLOCK_WOODEN_TRAPDOOR_CLOSE,
    BLOCK_WOODEN_TRAPDOOR_OPEN,
    BLOCK_WOOD_BREAK("DIG_WOOD"),
    BLOCK_WOOD_FALL,
    BLOCK_WOOD_HIT,
    BLOCK_WOOD_PLACE,
    BLOCK_WOOD_STEP("STEP_WOOD"),
    BLOCK_WOOL_BREAK("DIG_WOOL", "BLOCK_CLOTH_BREAK"),
    BLOCK_WOOL_FALL,
    BLOCK_WOOL_HIT("BLOCK_WOOL_FALL"),
    BLOCK_WOOL_PLACE("BLOCK_WOOL_FALL"),
    BLOCK_WOOL_STEP("STEP_WOOL", "BLOCK_CLOTH_STEP"),
    ENCHANT_THORNS_HIT,
    ENTITY_ALLAY_AMBIENT_WITHOUT_ITEM,
    ENTITY_ALLAY_AMBIENT_WITH_ITEM,
    ENTITY_ALLAY_DEATH,
    ENTITY_ALLAY_HURT,
    ENTITY_ALLAY_ITEM_GIVEN,
    ENTITY_ALLAY_ITEM_TAKEN,
    ENTITY_ALLAY_ITEM_THROWN,
    ENTITY_ARMOR_STAND_BREAK("ENTITY_ARMORSTAND_BREAK"),
    ENTITY_ARMOR_STAND_FALL("ENTITY_ARMORSTAND_FALL"),
    ENTITY_ARMOR_STAND_HIT("ENTITY_ARMORSTAND_HIT"),
    ENTITY_ARMOR_STAND_PLACE("ENTITY_ARMORSTAND_PLACE"),
    ENTITY_ARROW_HIT("ARROW_HIT"),
    ENTITY_ARROW_HIT_PLAYER("SUCCESSFUL_HIT"),
    ENTITY_ARROW_SHOOT("SHOOT_ARROW"),
    ENTITY_AXOLOTL_ATTACK,
    ENTITY_AXOLOTL_DEATH,
    ENTITY_AXOLOTL_HURT,
    ENTITY_AXOLOTL_IDLE_AIR,
    ENTITY_AXOLOTL_IDLE_WATER,
    ENTITY_AXOLOTL_SPLASH,
    ENTITY_AXOLOTL_SWIM,
    ENTITY_BAT_AMBIENT("BAT_IDLE"),
    ENTITY_BAT_DEATH("BAT_DEATH"),
    ENTITY_BAT_HURT("BAT_HURT"),
    ENTITY_BAT_LOOP("BAT_LOOP"),
    ENTITY_BAT_TAKEOFF("BAT_TAKEOFF"),
    ENTITY_BEE_DEATH,
    ENTITY_BEE_HURT,
    ENTITY_BEE_LOOP,
    ENTITY_BEE_LOOP_AGGRESSIVE,
    ENTITY_BEE_POLLINATE,
    ENTITY_BEE_STING,
    ENTITY_BLAZE_AMBIENT("BLAZE_BREATH"),
    ENTITY_BLAZE_BURN,
    ENTITY_BLAZE_DEATH("BLAZE_DEATH"),
    ENTITY_BLAZE_HURT("BLAZE_HIT"),
    ENTITY_BLAZE_SHOOT,
    ENTITY_BOAT_PADDLE_LAND,
    ENTITY_BOAT_PADDLE_WATER,
    ENTITY_CAMEL_AMBIENT,
    ENTITY_CAMEL_DASH,
    ENTITY_CAMEL_DASH_READY,
    ENTITY_CAMEL_DEATH,
    ENTITY_CAMEL_EAT,
    ENTITY_CAMEL_HURT,
    ENTITY_CAMEL_SADDLE,
    ENTITY_CAMEL_SIT,
    ENTITY_CAMEL_STAND,
    ENTITY_CAMEL_STEP,
    ENTITY_CAMEL_STEP_SAND,
    ENTITY_CAT_AMBIENT("CAT_MEOW"),
    ENTITY_CAT_BEG_FOR_FOOD,
    ENTITY_CAT_DEATH,
    ENTITY_CAT_EAT,
    ENTITY_CAT_HISS("CAT_HISS"),
    ENTITY_CAT_HURT("CAT_HIT"),
    ENTITY_CAT_PURR("CAT_PURR"),
    ENTITY_CAT_PURREOW("CAT_PURREOW"),
    ENTITY_CAT_STRAY_AMBIENT,
    ENTITY_CHICKEN_AMBIENT("CHICKEN_IDLE"),
    ENTITY_CHICKEN_DEATH,
    ENTITY_CHICKEN_EGG("CHICKEN_EGG_POP"),
    ENTITY_CHICKEN_HURT("CHICKEN_HURT"),
    ENTITY_CHICKEN_STEP("CHICKEN_WALK"),
    ENTITY_COD_AMBIENT,
    ENTITY_COD_DEATH,
    ENTITY_COD_FLOP,
    ENTITY_COD_HURT,
    ENTITY_COW_AMBIENT("COW_IDLE"),
    ENTITY_COW_DEATH,
    ENTITY_COW_HURT("COW_HURT"),
    ENTITY_COW_MILK,
    ENTITY_COW_STEP("COW_WALK"),
    ENTITY_CREEPER_DEATH("CREEPER_DEATH"),
    ENTITY_CREEPER_HURT,
    ENTITY_CREEPER_PRIMED("CREEPER_HISS"),
    ENTITY_DOLPHIN_AMBIENT,
    ENTITY_DOLPHIN_AMBIENT_WATER,
    ENTITY_DOLPHIN_ATTACK,
    ENTITY_DOLPHIN_DEATH,
    ENTITY_DOLPHIN_EAT,
    ENTITY_DOLPHIN_HURT,
    ENTITY_DOLPHIN_JUMP,
    ENTITY_DOLPHIN_PLAY,
    ENTITY_DOLPHIN_SPLASH,
    ENTITY_DOLPHIN_SWIM,
    ENTITY_DONKEY_AMBIENT("DONKEY_IDLE"),
    ENTITY_DONKEY_ANGRY("DONKEY_ANGRY"),
    ENTITY_DONKEY_CHEST,
    ENTITY_DONKEY_DEATH("DONKEY_DEATH"),
    ENTITY_DONKEY_EAT,
    ENTITY_DONKEY_HURT("DONKEY_HIT"),
    ENTITY_DRAGON_FIREBALL_EXPLODE("ENTITY_ENDERDRAGON_FIREBALL_EXPLODE"),
    ENTITY_DROWNED_AMBIENT,
    ENTITY_DROWNED_AMBIENT_WATER,
    ENTITY_DROWNED_DEATH,
    ENTITY_DROWNED_DEATH_WATER,
    ENTITY_DROWNED_HURT,
    ENTITY_DROWNED_HURT_WATER,
    ENTITY_DROWNED_SHOOT,
    ENTITY_DROWNED_STEP,
    ENTITY_DROWNED_SWIM,
    ENTITY_EGG_THROW,
    ENTITY_ELDER_GUARDIAN_AMBIENT,
    ENTITY_ELDER_GUARDIAN_AMBIENT_LAND,
    ENTITY_ELDER_GUARDIAN_CURSE,
    ENTITY_ELDER_GUARDIAN_DEATH,
    ENTITY_ELDER_GUARDIAN_DEATH_LAND,
    ENTITY_ELDER_GUARDIAN_FLOP,
    ENTITY_ELDER_GUARDIAN_HURT,
    ENTITY_ELDER_GUARDIAN_HURT_LAND,
    ENTITY_ENDERMAN_AMBIENT("ENDERMAN_IDLE", "ENTITY_ENDERMEN_AMBIENT"),
    ENTITY_ENDERMAN_DEATH("ENDERMAN_DEATH", "ENTITY_ENDERMEN_DEATH"),
    ENTITY_ENDERMAN_HURT("ENDERMAN_HIT", "ENTITY_ENDERMEN_HURT"),
    ENTITY_ENDERMAN_SCREAM("ENDERMAN_SCREAM", "ENTITY_ENDERMEN_SCREAM"),
    ENTITY_ENDERMAN_STARE("ENDERMAN_STARE", "ENTITY_ENDERMEN_STARE"),
    ENTITY_ENDERMAN_TELEPORT("ENDERMAN_TELEPORT", "ENTITY_ENDERMEN_TELEPORT"),
    ENTITY_ENDERMITE_AMBIENT,
    ENTITY_ENDERMITE_DEATH,
    ENTITY_ENDERMITE_HURT,
    ENTITY_ENDERMITE_STEP,
    ENTITY_ENDER_DRAGON_AMBIENT("ENDERDRAGON_WINGS", "ENTITY_ENDERDRAGON_AMBIENT"),
    ENTITY_ENDER_DRAGON_DEATH("ENDERDRAGON_DEATH", "ENTITY_ENDERDRAGON_DEATH"),
    ENTITY_ENDER_DRAGON_FLAP("ENDERDRAGON_WINGS", "ENTITY_ENDERDRAGON_FLAP"),
    ENTITY_ENDER_DRAGON_GROWL("ENDERDRAGON_GROWL", "ENTITY_ENDERDRAGON_GROWL"),
    ENTITY_ENDER_DRAGON_HURT("ENDERDRAGON_HIT", "ENTITY_ENDERDRAGON_HURT"),
    ENTITY_ENDER_DRAGON_SHOOT("ENTITY_ENDERDRAGON_SHOOT"),
    ENTITY_ENDER_EYE_DEATH,
    ENTITY_ENDER_EYE_LAUNCH("ENTITY_ENDER_EYE_DEATH", "ENTITY_ENDEREYE_DEATH"),
    ENTITY_ENDER_PEARL_THROW("ENTITY_ENDERPEARL_THROW"),
    ENTITY_EVOKER_AMBIENT("ENTITY_EVOCATION_ILLAGER_AMBIENT"),
    ENTITY_EVOKER_CAST_SPELL("ENTITY_EVOCATION_ILLAGER_CAST_SPELL"),
    ENTITY_EVOKER_CELEBRATE,
    ENTITY_EVOKER_DEATH("ENTITY_EVOCATION_ILLAGER_DEATH"),
    ENTITY_EVOKER_FANGS_ATTACK("ENTITY_EVOCATION_FANGS_ATTACK"),
    ENTITY_EVOKER_HURT("ENTITY_EVOCATION_ILLAGER_HURT"),
    ENTITY_EVOKER_PREPARE_ATTACK("ENTITY_EVOCATION_ILLAGER_PREPARE_ATTACK"),
    ENTITY_EVOKER_PREPARE_SUMMON("ENTITY_EVOCATION_ILLAGER_PREPARE_SUMMON"),
    ENTITY_EVOKER_PREPARE_WOLOLO("ENTITY_EVOCATION_ILLAGER_PREPARE_WOLOLO"),
    ENTITY_EXPERIENCE_BOTTLE_THROW,
    ENTITY_EXPERIENCE_ORB_PICKUP("ORB_PICKUP"),
    ENTITY_FIREWORK_ROCKET_BLAST("FIREWORK_BLAST", "ENTITY_FIREWORK_BLAST"),
    ENTITY_FIREWORK_ROCKET_BLAST_FAR("FIREWORK_BLAST2", "ENTITY_FIREWORK_BLAST_FAR"),
    ENTITY_FIREWORK_ROCKET_LARGE_BLAST("FIREWORK_LARGE_BLAST", "ENTITY_FIREWORK_LARGE_BLAST"),
    ENTITY_FIREWORK_ROCKET_LARGE_BLAST_FAR("FIREWORK_LARGE_BLAST2", "ENTITY_FIREWORK_LARGE_BLAST_FAR"),
    ENTITY_FIREWORK_ROCKET_LAUNCH("FIREWORK_LAUNCH", "ENTITY_FIREWORK_LAUNCH"),
    ENTITY_FIREWORK_ROCKET_SHOOT,
    ENTITY_FIREWORK_ROCKET_TWINKLE("FIREWORK_TWINKLE", "ENTITY_FIREWORK_TWINKLE"),
    ENTITY_FIREWORK_ROCKET_TWINKLE_FAR("FIREWORK_TWINKLE2", "ENTITY_FIREWORK_TWINKLE_FAR"),
    ENTITY_FISHING_BOBBER_RETRIEVE,
    ENTITY_FISHING_BOBBER_SPLASH("SPLASH2", "ENTITY_BOBBER_SPLASH"),
    ENTITY_FISHING_BOBBER_THROW("ENTITY_BOBBER_THROW"),
    ENTITY_FISH_SWIM,
    ENTITY_FOX_AGGRO,
    ENTITY_FOX_AMBIENT,
    ENTITY_FOX_BITE,
    ENTITY_FOX_DEATH,
    ENTITY_FOX_EAT,
    ENTITY_FOX_HURT,
    ENTITY_FOX_SCREECH,
    ENTITY_FOX_SLEEP,
    ENTITY_FOX_SNIFF,
    ENTITY_FOX_SPIT,
    ENTITY_FOX_TELEPORT,
    ENTITY_FROG_AMBIENT,
    ENTITY_FROG_DEATH,
    ENTITY_FROG_EAT,
    ENTITY_FROG_HURT,
    ENTITY_FROG_LAY_SPAWN,
    ENTITY_FROG_LONG_JUMP,
    ENTITY_FROG_STEP,
    ENTITY_FROG_TONGUE,
    ENTITY_GENERIC_BIG_FALL("FALL_BIG"),
    ENTITY_GENERIC_BURN,
    ENTITY_GENERIC_DEATH,
    ENTITY_GENERIC_DRINK("DRINK"),
    ENTITY_GENERIC_EAT("EAT"),
    ENTITY_GENERIC_EXPLODE("EXPLODE"),
    ENTITY_GENERIC_EXTINGUISH_FIRE,
    ENTITY_GENERIC_HURT,
    ENTITY_GENERIC_SMALL_FALL("FALL_SMALL"),
    ENTITY_GENERIC_SPLASH("SPLASH"),
    ENTITY_GENERIC_SWIM("SWIM"),
    ENTITY_GHAST_AMBIENT("GHAST_MOAN"),
    ENTITY_GHAST_DEATH("GHAST_DEATH"),
    ENTITY_GHAST_HURT("GHAST_SCREAM2"),
    ENTITY_GHAST_SCREAM("GHAST_SCREAM"),
    ENTITY_GHAST_SHOOT("GHAST_FIREBALL"),
    ENTITY_GHAST_WARN("GHAST_CHARGE"),
    ENTITY_GLOW_ITEM_FRAME_ADD_ITEM,
    ENTITY_GLOW_ITEM_FRAME_BREAK,
    ENTITY_GLOW_ITEM_FRAME_PLACE,
    ENTITY_GLOW_ITEM_FRAME_REMOVE_ITEM,
    ENTITY_GLOW_ITEM_FRAME_ROTATE_ITEM,
    ENTITY_GLOW_SQUID_AMBIENT,
    ENTITY_GLOW_SQUID_DEATH,
    ENTITY_GLOW_SQUID_HURT,
    ENTITY_GLOW_SQUID_SQUIRT,
    ENTITY_GOAT_AMBIENT,
    ENTITY_GOAT_DEATH,
    ENTITY_GOAT_EAT,
    ENTITY_GOAT_HORN_BREAK,
    ENTITY_GOAT_HURT,
    ENTITY_GOAT_LONG_JUMP,
    ENTITY_GOAT_MILK,
    ENTITY_GOAT_PREPARE_RAM,
    ENTITY_GOAT_RAM_IMPACT,
    ENTITY_GOAT_SCREAMING_AMBIENT,
    ENTITY_GOAT_SCREAMING_DEATH,
    ENTITY_GOAT_SCREAMING_EAT,
    ENTITY_GOAT_SCREAMING_HORN_BREAK,
    ENTITY_GOAT_SCREAMING_HURT,
    ENTITY_GOAT_SCREAMING_LONG_JUMP,
    ENTITY_GOAT_SCREAMING_MILK,
    ENTITY_GOAT_SCREAMING_PREPARE_RAM,
    ENTITY_GOAT_SCREAMING_RAM_IMPACT,
    ENTITY_GOAT_STEP,
    ENTITY_GUARDIAN_AMBIENT,
    ENTITY_GUARDIAN_AMBIENT_LAND,
    ENTITY_GUARDIAN_ATTACK,
    ENTITY_GUARDIAN_DEATH,
    ENTITY_GUARDIAN_DEATH_LAND,
    ENTITY_GUARDIAN_FLOP,
    ENTITY_GUARDIAN_HURT,
    ENTITY_GUARDIAN_HURT_LAND,
    ENTITY_HOGLIN_AMBIENT,
    ENTITY_HOGLIN_ANGRY,
    ENTITY_HOGLIN_ATTACK,
    ENTITY_HOGLIN_CONVERTED_TO_ZOMBIFIED,
    ENTITY_HOGLIN_DEATH,
    ENTITY_HOGLIN_HURT,
    ENTITY_HOGLIN_RETREAT,
    ENTITY_HOGLIN_STEP,
    ENTITY_HORSE_AMBIENT("HORSE_IDLE"),
    ENTITY_HORSE_ANGRY("HORSE_ANGRY"),
    ENTITY_HORSE_ARMOR("HORSE_ARMOR"),
    ENTITY_HORSE_BREATHE("HORSE_BREATHE"),
    ENTITY_HORSE_DEATH("HORSE_DEATH"),
    ENTITY_HORSE_EAT,
    ENTITY_HORSE_GALLOP("HORSE_GALLOP"),
    ENTITY_HORSE_HURT("HORSE_HIT"),
    ENTITY_HORSE_JUMP("HORSE_JUMP"),
    ENTITY_HORSE_LAND("HORSE_LAND"),
    ENTITY_HORSE_SADDLE("HORSE_SADDLE"),
    ENTITY_HORSE_STEP("HORSE_SOFT"),
    ENTITY_HORSE_STEP_WOOD("HORSE_WOOD"),
    ENTITY_HOSTILE_BIG_FALL("FALL_BIG"),
    ENTITY_HOSTILE_DEATH,
    ENTITY_HOSTILE_HURT,
    ENTITY_HOSTILE_SMALL_FALL("FALL_SMALL"),
    ENTITY_HOSTILE_SPLASH("SPLASH"),
    ENTITY_HOSTILE_SWIM("SWIM"),
    ENTITY_HUSK_AMBIENT,
    ENTITY_HUSK_CONVERTED_TO_ZOMBIE,
    ENTITY_HUSK_DEATH,
    ENTITY_HUSK_HURT,
    ENTITY_HUSK_STEP,
    ENTITY_ILLUSIONER_AMBIENT("ENTITY_ILLUSION_ILLAGER_AMBIENT"),
    ENTITY_ILLUSIONER_CAST_SPELL("ENTITY_ILLUSION_ILLAGER_CAST_SPELL"),
    ENTITY_ILLUSIONER_DEATH("ENTITY_ILLUSIONER_CAST_DEATH", "ENTITY_ILLUSION_ILLAGER_DEATH"),
    ENTITY_ILLUSIONER_HURT("ENTITY_ILLUSION_ILLAGER_HURT"),
    ENTITY_ILLUSIONER_MIRROR_MOVE("ENTITY_ILLUSION_ILLAGER_MIRROR_MOVE"),
    ENTITY_ILLUSIONER_PREPARE_BLINDNESS("ENTITY_ILLUSION_ILLAGER_PREPARE_BLINDNESS"),
    ENTITY_ILLUSIONER_PREPARE_MIRROR("ENTITY_ILLUSION_ILLAGER_PREPARE_MIRROR"),
    ENTITY_IRON_GOLEM_ATTACK("IRONGOLEM_THROW", "ENTITY_IRONGOLEM_ATTACK"),
    ENTITY_IRON_GOLEM_DAMAGE,
    ENTITY_IRON_GOLEM_DEATH("IRONGOLEM_DEATH", "ENTITY_IRONGOLEM_DEATH"),
    ENTITY_IRON_GOLEM_HURT("IRONGOLEM_HIT", "ENTITY_IRONGOLEM_HURT"),
    ENTITY_IRON_GOLEM_REPAIR,
    ENTITY_IRON_GOLEM_STEP("IRONGOLEM_WALK", "ENTITY_IRONGOLEM_STEP"),
    ENTITY_ITEM_BREAK("ITEM_BREAK"),
    ENTITY_ITEM_FRAME_ADD_ITEM("ENTITY_ITEMFRAME_ADD_ITEM"),
    ENTITY_ITEM_FRAME_BREAK("ENTITY_ITEMFRAME_BREAK"),
    ENTITY_ITEM_FRAME_PLACE("ENTITY_ITEMFRAME_PLACE"),
    ENTITY_ITEM_FRAME_REMOVE_ITEM("ENTITY_ITEMFRAME_REMOVE_ITEM"),
    ENTITY_ITEM_FRAME_ROTATE_ITEM("ENTITY_ITEMFRAME_ROTATE_ITEM"),
    ENTITY_ITEM_PICKUP("ITEM_PICKUP"),
    ENTITY_LEASH_KNOT_BREAK("ENTITY_LEASHKNOT_BREAK"),
    ENTITY_LEASH_KNOT_PLACE("ENTITY_LEASHKNOT_PLACE"),
    ENTITY_LIGHTNING_BOLT_IMPACT("AMBIENCE_THUNDER", "ENTITY_LIGHTNING_IMPACT"),
    ENTITY_LIGHTNING_BOLT_THUNDER("AMBIENCE_THUNDER", "ENTITY_LIGHTNING_THUNDER"),
    ENTITY_LINGERING_POTION_THROW,
    ENTITY_LLAMA_AMBIENT,
    ENTITY_LLAMA_ANGRY,
    ENTITY_LLAMA_CHEST,
    ENTITY_LLAMA_DEATH,
    ENTITY_LLAMA_EAT,
    ENTITY_LLAMA_HURT,
    ENTITY_LLAMA_SPIT,
    ENTITY_LLAMA_STEP,
    ENTITY_LLAMA_SWAG,
    ENTITY_MAGMA_CUBE_DEATH("ENTITY_MAGMACUBE_DEATH"),
    ENTITY_MAGMA_CUBE_DEATH_SMALL("ENTITY_SMALL_MAGMACUBE_DEATH"),
    ENTITY_MAGMA_CUBE_HURT("ENTITY_MAGMACUBE_HURT"),
    ENTITY_MAGMA_CUBE_HURT_SMALL("ENTITY_SMALL_MAGMACUBE_HURT"),
    ENTITY_MAGMA_CUBE_JUMP("MAGMACUBE_JUMP", "ENTITY_MAGMACUBE_JUMP"),
    ENTITY_MAGMA_CUBE_SQUISH("MAGMACUBE_WALK", "ENTITY_MAGMACUBE_SQUISH"),
    ENTITY_MAGMA_CUBE_SQUISH_SMALL("MAGMACUBE_WALK2", "ENTITY_SMALL_MAGMACUBE_SQUISH"),
    ENTITY_MINECART_INSIDE("MINECART_INSIDE"),
    ENTITY_MINECART_INSIDE_UNDERWATER,
    ENTITY_MINECART_RIDING("MINECART_BASE"),
    ENTITY_MOOSHROOM_CONVERT,
    ENTITY_MOOSHROOM_EAT,
    ENTITY_MOOSHROOM_MILK,
    ENTITY_MOOSHROOM_SHEAR,
    ENTITY_MOOSHROOM_SUSPICIOUS_MILK,
    ENTITY_MULE_AMBIENT,
    ENTITY_MULE_ANGRY,
    ENTITY_MULE_CHEST("ENTITY_MULE_AMBIENT"),
    ENTITY_MULE_DEATH("ENTITY_MULE_AMBIENT"),
    ENTITY_MULE_EAT,
    ENTITY_MULE_HURT("ENTITY_MULE_AMBIENT"),
    ENTITY_OCELOT_AMBIENT,
    ENTITY_OCELOT_DEATH,
    ENTITY_OCELOT_HURT,
    ENTITY_PAINTING_BREAK,
    ENTITY_PAINTING_PLACE,
    ENTITY_PANDA_AGGRESSIVE_AMBIENT,
    ENTITY_PANDA_AMBIENT,
    ENTITY_PANDA_BITE,
    ENTITY_PANDA_CANT_BREED,
    ENTITY_PANDA_DEATH,
    ENTITY_PANDA_EAT,
    ENTITY_PANDA_HURT,
    ENTITY_PANDA_PRE_SNEEZE,
    ENTITY_PANDA_SNEEZE,
    ENTITY_PANDA_STEP,
    ENTITY_PANDA_WORRIED_AMBIENT,
    ENTITY_PARROT_AMBIENT,
    ENTITY_PARROT_DEATH,
    ENTITY_PARROT_EAT,
    ENTITY_PARROT_FLY,
    ENTITY_PARROT_HURT,
    ENTITY_PARROT_IMITATE_BLAZE,
    ENTITY_PARROT_IMITATE_CREEPER,
    ENTITY_PARROT_IMITATE_DROWNED,
    ENTITY_PARROT_IMITATE_ELDER_GUARDIAN,
    /**
     * Removed in 1.15
     */
    ENTITY_PARROT_IMITATE_ENDERMAN,
    ENTITY_PARROT_IMITATE_ENDERMITE,
    ENTITY_PARROT_IMITATE_ENDER_DRAGON,
    ENTITY_PARROT_IMITATE_EVOKER,
    ENTITY_PARROT_IMITATE_GHAST,
    ENTITY_PARROT_IMITATE_GUARDIAN,
    ENTITY_PARROT_IMITATE_HOGLIN,
    ENTITY_PARROT_IMITATE_HUSK,
    ENTITY_PARROT_IMITATE_ILLUSIONER,
    ENTITY_PARROT_IMITATE_MAGMA_CUBE,
    ENTITY_PARROT_IMITATE_PHANTOM,
    ENTITY_PARROT_IMITATE_PIGLIN,
    ENTITY_PARROT_IMITATE_PIGLIN_BRUTE,
    ENTITY_PARROT_IMITATE_PILLAGER,
    /**
     * Removed in 1.15
     */
    ENTITY_PARROT_IMITATE_POLAR_BEAR,
    ENTITY_PARROT_IMITATE_RAVAGER,
    ENTITY_PARROT_IMITATE_SHULKER,
    ENTITY_PARROT_IMITATE_SILVERFISH,
    ENTITY_PARROT_IMITATE_SKELETON,
    ENTITY_PARROT_IMITATE_SLIME,
    ENTITY_PARROT_IMITATE_SPIDER,
    ENTITY_PARROT_IMITATE_STRAY,
    ENTITY_PARROT_IMITATE_VEX,
    ENTITY_PARROT_IMITATE_VINDICATOR,
    ENTITY_PARROT_IMITATE_WARDEN,
    ENTITY_PARROT_IMITATE_WITCH,
    ENTITY_PARROT_IMITATE_WITHER,
    ENTITY_PARROT_IMITATE_WITHER_SKELETON,
    /**
     * Removed in 1.15
     */
    ENTITY_PARROT_IMITATE_WOLF,
    ENTITY_PARROT_IMITATE_ZOGLIN,
    ENTITY_PARROT_IMITATE_ZOMBIE,
    ENTITY_PARROT_IMITATE_ZOMBIE_VILLAGER,
    ENTITY_PARROT_STEP,
    ENTITY_PHANTOM_AMBIENT,
    ENTITY_PHANTOM_BITE,
    ENTITY_PHANTOM_DEATH,
    ENTITY_PHANTOM_FLAP,
    ENTITY_PHANTOM_HURT,
    ENTITY_PHANTOM_SWOOP,
    ENTITY_PIGLIN_ADMIRING_ITEM,
    ENTITY_PIGLIN_AMBIENT,
    ENTITY_PIGLIN_ANGRY,
    ENTITY_PIGLIN_BRUTE_AMBIENT,
    ENTITY_PIGLIN_BRUTE_ANGRY,
    ENTITY_PIGLIN_BRUTE_CONVERTED_TO_ZOMBIFIED,
    ENTITY_PIGLIN_BRUTE_DEATH,
    ENTITY_PIGLIN_BRUTE_HURT,
    ENTITY_PIGLIN_BRUTE_STEP,
    ENTITY_PIGLIN_CELEBRATE,
    ENTITY_PIGLIN_CONVERTED_TO_ZOMBIFIED,
    ENTITY_PIGLIN_DEATH,
    ENTITY_PIGLIN_HURT,
    ENTITY_PIGLIN_JEALOUS,
    ENTITY_PIGLIN_RETREAT,
    ENTITY_PIGLIN_STEP,
    ENTITY_PIG_AMBIENT("PIG_IDLE"),
    ENTITY_PIG_DEATH("PIG_DEATH"),
    ENTITY_PIG_HURT,
    ENTITY_PIG_SADDLE("ENTITY_PIG_HURT"),
    ENTITY_PIG_STEP("PIG_WALK"),
    ENTITY_PILLAGER_AMBIENT,
    ENTITY_PILLAGER_CELEBRATE,
    ENTITY_PILLAGER_DEATH,
    ENTITY_PILLAGER_HURT,
    ENTITY_PLAYER_ATTACK_CRIT,
    ENTITY_PLAYER_ATTACK_KNOCKBACK,
    ENTITY_PLAYER_ATTACK_NODAMAGE,
    ENTITY_PLAYER_ATTACK_STRONG("SUCCESSFUL_HIT"),
    ENTITY_PLAYER_ATTACK_SWEEP,
    ENTITY_PLAYER_ATTACK_WEAK,
    ENTITY_PLAYER_BIG_FALL("FALL_BIG"),
    ENTITY_PLAYER_BREATH,
    ENTITY_PLAYER_BURP("BURP"),
    ENTITY_PLAYER_DEATH,
    ENTITY_PLAYER_HURT("HURT_FLESH"),
    ENTITY_PLAYER_HURT_DROWN,
    ENTITY_PLAYER_HURT_FREEZE,
    ENTITY_PLAYER_HURT_ON_FIRE,
    ENTITY_PLAYER_HURT_SWEET_BERRY_BUSH,
    ENTITY_PLAYER_LEVELUP("LEVEL_UP"),
    ENTITY_PLAYER_SMALL_FALL("FALL_SMALL"),
    ENTITY_PLAYER_SPLASH("SLASH"),
    ENTITY_PLAYER_SPLASH_HIGH_SPEED("SPLASH"),
    ENTITY_PLAYER_SWIM("SWIM"),
    ENTITY_POLAR_BEAR_AMBIENT,
    ENTITY_POLAR_BEAR_AMBIENT_BABY("ENTITY_POLAR_BEAR_BABY_AMBIENT"),
    ENTITY_POLAR_BEAR_DEATH,
    ENTITY_POLAR_BEAR_HURT,
    ENTITY_POLAR_BEAR_STEP,
    ENTITY_POLAR_BEAR_WARNING,
    ENTITY_PUFFER_FISH_AMBIENT,
    ENTITY_PUFFER_FISH_BLOW_OUT,
    ENTITY_PUFFER_FISH_BLOW_UP,
    ENTITY_PUFFER_FISH_DEATH,
    ENTITY_PUFFER_FISH_FLOP,
    ENTITY_PUFFER_FISH_HURT,
    ENTITY_PUFFER_FISH_STING,
    ENTITY_RABBIT_AMBIENT,
    ENTITY_RABBIT_ATTACK,
    ENTITY_RABBIT_DEATH,
    ENTITY_RABBIT_HURT,
    ENTITY_RABBIT_JUMP,
    ENTITY_RAVAGER_AMBIENT,
    ENTITY_RAVAGER_ATTACK,
    ENTITY_RAVAGER_CELEBRATE,
    ENTITY_RAVAGER_DEATH,
    ENTITY_RAVAGER_HURT,
    ENTITY_RAVAGER_ROAR,
    ENTITY_RAVAGER_STEP,
    ENTITY_RAVAGER_STUNNED,
    ENTITY_SALMON_AMBIENT,
    ENTITY_SALMON_DEATH,
    ENTITY_SALMON_FLOP,
    ENTITY_SALMON_HURT("ENTITY_SALMON_FLOP"),
    ENTITY_SHEEP_AMBIENT("SHEEP_IDLE"),
    ENTITY_SHEEP_DEATH,
    ENTITY_SHEEP_HURT,
    ENTITY_SHEEP_SHEAR("SHEEP_SHEAR"),
    ENTITY_SHEEP_STEP("SHEEP_WALK"),
    ENTITY_SHULKER_AMBIENT,
    ENTITY_SHULKER_BULLET_HIT,
    ENTITY_SHULKER_BULLET_HURT,
    ENTITY_SHULKER_CLOSE,
    ENTITY_SHULKER_DEATH,
    ENTITY_SHULKER_HURT,
    ENTITY_SHULKER_HURT_CLOSED,
    ENTITY_SHULKER_OPEN,
    ENTITY_SHULKER_SHOOT,
    ENTITY_SHULKER_TELEPORT,
    ENTITY_SILVERFISH_AMBIENT("SILVERFISH_IDLE"),
    ENTITY_SILVERFISH_DEATH("SILVERFISH_KILL"),
    ENTITY_SILVERFISH_HURT("SILVERFISH_HIT"),
    ENTITY_SILVERFISH_STEP("SILVERFISH_WALK"),
    ENTITY_SKELETON_AMBIENT("SKELETON_IDLE"),
    ENTITY_SKELETON_CONVERTED_TO_STRAY,
    ENTITY_SKELETON_DEATH("SKELETON_DEATH"),
    ENTITY_SKELETON_HORSE_AMBIENT("HORSE_SKELETON_IDLE"),
    ENTITY_SKELETON_HORSE_AMBIENT_WATER,
    ENTITY_SKELETON_HORSE_DEATH("HORSE_SKELETON_DEATH"),
    ENTITY_SKELETON_HORSE_GALLOP_WATER,
    ENTITY_SKELETON_HORSE_HURT("HORSE_SKELETON_HIT"),
    ENTITY_SKELETON_HORSE_JUMP_WATER,
    ENTITY_SKELETON_HORSE_STEP_WATER,
    ENTITY_SKELETON_HORSE_SWIM,
    ENTITY_SKELETON_HURT("SKELETON_HURT"),
    ENTITY_SKELETON_SHOOT,
    ENTITY_SKELETON_STEP("SKELETON_WALK"),
    ENTITY_SLIME_ATTACK("SLIME_ATTACK"),
    ENTITY_SLIME_DEATH,
    ENTITY_SLIME_DEATH_SMALL,
    ENTITY_SLIME_HURT,
    ENTITY_SLIME_HURT_SMALL("ENTITY_SMALL_SLIME_HURT"),
    ENTITY_SLIME_JUMP("SLIME_WALK"),
    ENTITY_SLIME_JUMP_SMALL("SLIME_WALK2", "ENTITY_SMALL_SLIME_SQUISH"),
    ENTITY_SLIME_SQUISH("SLIME_WALK2"),
    ENTITY_SLIME_SQUISH_SMALL("ENTITY_SMALL_SLIME_SQUISH"),
    ENTITY_SNIFFER_DEATH,
    ENTITY_SNIFFER_DIGGING,
    ENTITY_SNIFFER_DIGGING_STOP,
    ENTITY_SNIFFER_DROP_SEED,
    ENTITY_SNIFFER_EAT,
    ENTITY_SNIFFER_HAPPY,
    ENTITY_SNIFFER_HURT,
    ENTITY_SNIFFER_IDLE,
    ENTITY_SNIFFER_SCENTING,
    ENTITY_SNIFFER_SEARCHING,
    ENTITY_SNIFFER_SNIFFING,
    ENTITY_SNIFFER_STEP,
    ENTITY_SNOWBALL_THROW,
    ENTITY_SNOW_GOLEM_AMBIENT("ENTITY_SNOWMAN_AMBIENT"),
    ENTITY_SNOW_GOLEM_DEATH("ENTITY_SNOWMAN_DEATH"),
    ENTITY_SNOW_GOLEM_HURT("ENTITY_SNOWMAN_HURT"),
    ENTITY_SNOW_GOLEM_SHEAR,
    ENTITY_SNOW_GOLEM_SHOOT("ENTITY_SNOWMAN_SHOOT"),
    ENTITY_SPIDER_AMBIENT("SPIDER_IDLE"),
    ENTITY_SPIDER_DEATH("SPIDER_DEATH"),
    ENTITY_SPIDER_HURT,
    ENTITY_SPIDER_STEP("SPIDER_WALK"),
    ENTITY_SPLASH_POTION_BREAK,
    ENTITY_SPLASH_POTION_THROW,
    ENTITY_SQUID_AMBIENT,
    ENTITY_SQUID_DEATH,
    ENTITY_SQUID_HURT,
    ENTITY_SQUID_SQUIRT,
    ENTITY_STRAY_AMBIENT,
    ENTITY_STRAY_DEATH,
    ENTITY_STRAY_HURT,
    ENTITY_STRAY_STEP,
    ENTITY_STRIDER_AMBIENT,
    ENTITY_STRIDER_DEATH,
    ENTITY_STRIDER_EAT,
    ENTITY_STRIDER_HAPPY,
    ENTITY_STRIDER_HURT,
    ENTITY_STRIDER_RETREAT,
    ENTITY_STRIDER_SADDLE,
    ENTITY_STRIDER_STEP,
    ENTITY_STRIDER_STEP_LAVA,
    ENTITY_TADPOLE_DEATH,
    ENTITY_TADPOLE_FLOP,
    ENTITY_TADPOLE_GROW_UP,
    ENTITY_TADPOLE_HURT,
    ENTITY_TNT_PRIMED("FUSE"),
    ENTITY_TROPICAL_FISH_AMBIENT,
    ENTITY_TROPICAL_FISH_DEATH,
    ENTITY_TROPICAL_FISH_FLOP("ENTITY_TROPICAL_FISH_DEATH"),
    ENTITY_TROPICAL_FISH_HURT,
    ENTITY_TURTLE_AMBIENT_LAND,
    ENTITY_TURTLE_DEATH,
    ENTITY_TURTLE_DEATH_BABY,
    ENTITY_TURTLE_EGG_BREAK,
    ENTITY_TURTLE_EGG_CRACK,
    ENTITY_TURTLE_EGG_HATCH,
    ENTITY_TURTLE_HURT,
    ENTITY_TURTLE_HURT_BABY,
    ENTITY_TURTLE_LAY_EGG,
    ENTITY_TURTLE_SHAMBLE,
    ENTITY_TURTLE_SHAMBLE_BABY,
    ENTITY_TURTLE_SWIM,
    ENTITY_VEX_AMBIENT,
    ENTITY_VEX_CHARGE,
    ENTITY_VEX_DEATH,
    ENTITY_VEX_HURT,
    ENTITY_VILLAGER_AMBIENT("VILLAGER_IDLE"),
    ENTITY_VILLAGER_CELEBRATE,
    ENTITY_VILLAGER_DEATH("VILLAGER_DEATH"),
    ENTITY_VILLAGER_HURT("VILLAGER_HIT"),
    ENTITY_VILLAGER_NO("VILLAGER_NO"),
    ENTITY_VILLAGER_TRADE("VILLAGER_HAGGLE", "ENTITY_VILLAGER_TRADING"),
    ENTITY_VILLAGER_WORK_ARMORER,
    ENTITY_VILLAGER_WORK_BUTCHER,
    ENTITY_VILLAGER_WORK_CARTOGRAPHER,
    ENTITY_VILLAGER_WORK_CLERIC,
    ENTITY_VILLAGER_WORK_FARMER,
    ENTITY_VILLAGER_WORK_FISHERMAN,
    ENTITY_VILLAGER_WORK_FLETCHER,
    ENTITY_VILLAGER_WORK_LEATHERWORKER,
    ENTITY_VILLAGER_WORK_LIBRARIAN,
    ENTITY_VILLAGER_WORK_MASON,
    ENTITY_VILLAGER_WORK_SHEPHERD,
    ENTITY_VILLAGER_WORK_TOOLSMITH,
    ENTITY_VILLAGER_WORK_WEAPONSMITH,
    ENTITY_VILLAGER_YES("VILLAGER_YES"),
    ENTITY_VINDICATOR_AMBIENT("ENTITY_VINDICATION_ILLAGER_AMBIENT"),
    ENTITY_VINDICATOR_CELEBRATE,
    ENTITY_VINDICATOR_DEATH("ENTITY_VINDICATION_ILLAGER_DEATH"),
    ENTITY_VINDICATOR_HURT("ENTITY_VINDICATION_ILLAGER_HURT"),
    ENTITY_WANDERING_TRADER_AMBIENT,
    ENTITY_WANDERING_TRADER_DEATH,
    ENTITY_WANDERING_TRADER_DISAPPEARED,
    ENTITY_WANDERING_TRADER_DRINK_MILK,
    ENTITY_WANDERING_TRADER_DRINK_POTION,
    ENTITY_WANDERING_TRADER_HURT,
    ENTITY_WANDERING_TRADER_NO,
    ENTITY_WANDERING_TRADER_REAPPEARED,
    ENTITY_WANDERING_TRADER_TRADE,
    ENTITY_WANDERING_TRADER_YES,
    ENTITY_WARDEN_AGITATED,
    ENTITY_WARDEN_AMBIENT,
    ENTITY_WARDEN_ANGRY,
    ENTITY_WARDEN_ATTACK_IMPACT,
    ENTITY_WARDEN_DEATH,
    ENTITY_WARDEN_DIG,
    ENTITY_WARDEN_EMERGE,
    ENTITY_WARDEN_HEARTBEAT,
    ENTITY_WARDEN_HURT,
    ENTITY_WARDEN_LISTENING,
    ENTITY_WARDEN_LISTENING_ANGRY,
    ENTITY_WARDEN_NEARBY_CLOSE,
    ENTITY_WARDEN_NEARBY_CLOSER,
    ENTITY_WARDEN_NEARBY_CLOSEST,
    ENTITY_WARDEN_ROAR,
    ENTITY_WARDEN_SNIFF,
    ENTITY_WARDEN_SONIC_BOOM,
    ENTITY_WARDEN_SONIC_CHARGE,
    ENTITY_WARDEN_STEP,
    ENTITY_WARDEN_TENDRIL_CLICKS,
    ENTITY_WITCH_AMBIENT,
    ENTITY_WITCH_CELEBRATE,
    ENTITY_WITCH_DEATH,
    ENTITY_WITCH_DRINK,
    ENTITY_WITCH_HURT,
    ENTITY_WITCH_THROW,
    ENTITY_WITHER_AMBIENT("WITHER_IDLE"),
    ENTITY_WITHER_BREAK_BLOCK,
    ENTITY_WITHER_DEATH("WITHER_DEATH"),
    ENTITY_WITHER_HURT("WITHER_HURT"),
    ENTITY_WITHER_SHOOT("WITHER_SHOOT"),
    ENTITY_WITHER_SKELETON_AMBIENT,
    ENTITY_WITHER_SKELETON_DEATH,
    ENTITY_WITHER_SKELETON_HURT,
    ENTITY_WITHER_SKELETON_STEP,
    ENTITY_WITHER_SPAWN("WITHER_SPAWN"),
    ENTITY_WOLF_AMBIENT("WOLF_BARK"),
    ENTITY_WOLF_DEATH("WOLF_DEATH"),
    ENTITY_WOLF_GROWL("WOLF_GROWL"),
    ENTITY_WOLF_HOWL("WOLF_HOWL"),
    ENTITY_WOLF_HURT("WOLF_HURT"),
    ENTITY_WOLF_PANT("WOLF_PANT"),
    ENTITY_WOLF_SHAKE("WOLF_SHAKE"),
    ENTITY_WOLF_STEP("WOLF_WALK"),
    ENTITY_WOLF_WHINE("WOLF_WHINE"),
    ENTITY_ZOGLIN_AMBIENT,
    ENTITY_ZOGLIN_ANGRY,
    ENTITY_ZOGLIN_ATTACK,
    ENTITY_ZOGLIN_DEATH,
    ENTITY_ZOGLIN_HURT,
    ENTITY_ZOGLIN_STEP,
    ENTITY_ZOMBIE_AMBIENT("ZOMBIE_IDLE"),
    ENTITY_ZOMBIE_ATTACK_IRON_DOOR("ZOMBIE_METAL"),
    ENTITY_ZOMBIE_ATTACK_WOODEN_DOOR("ZOMBIE_WOOD", "ENTITY_ZOMBIE_ATTACK_DOOR_WOOD"),
    ENTITY_ZOMBIE_BREAK_WOODEN_DOOR("ZOMBIE_WOODBREAK", "ENTITY_ZOMBIE_BREAK_DOOR_WOOD"),
    ENTITY_ZOMBIE_CONVERTED_TO_DROWNED,
    ENTITY_ZOMBIE_DEATH("ZOMBIE_DEATH"),
    ENTITY_ZOMBIE_DESTROY_EGG,
    ENTITY_ZOMBIE_HORSE_AMBIENT("HORSE_ZOMBIE_IDLE"),
    ENTITY_ZOMBIE_HORSE_DEATH("HORSE_ZOMBIE_DEATH"),
    ENTITY_ZOMBIE_HORSE_HURT("HORSE_ZOMBIE_HIT"),
    ENTITY_ZOMBIE_HURT("ZOMBIE_HURT"),
    ENTITY_ZOMBIE_INFECT("ZOMBIE_INFECT"),
    ENTITY_ZOMBIE_STEP("ZOMBIE_WALK"),
    ENTITY_ZOMBIE_VILLAGER_AMBIENT,
    ENTITY_ZOMBIE_VILLAGER_CONVERTED("ZOMBIE_UNFECT"),
    ENTITY_ZOMBIE_VILLAGER_CURE("ZOMBIE_REMEDY"),
    ENTITY_ZOMBIE_VILLAGER_DEATH,
    ENTITY_ZOMBIE_VILLAGER_HURT,
    ENTITY_ZOMBIE_VILLAGER_STEP,
    ENTITY_ZOMBIFIED_PIGLIN_AMBIENT("ZOMBIE_PIG_IDLE", "ENTITY_ZOMBIE_PIG_AMBIENT", "ENTITY_ZOMBIE_PIGMAN_AMBIENT"),
    ENTITY_ZOMBIFIED_PIGLIN_ANGRY("ZOMBIE_PIG_ANGRY", "ENTITY_ZOMBIE_PIG_ANGRY", "ENTITY_ZOMBIE_PIGMAN_ANGRY"),
    ENTITY_ZOMBIFIED_PIGLIN_DEATH("ZOMBIE_PIG_DEATH", "ENTITY_ZOMBIE_PIG_DEATH", "ENTITY_ZOMBIE_PIGMAN_DEATH"),
    ENTITY_ZOMBIFIED_PIGLIN_HURT("ZOMBIE_PIG_HURT", "ENTITY_ZOMBIE_PIG_HURT", "ENTITY_ZOMBIE_PIGMAN_HURT"),
    EVENT_RAID_HORN,
    INTENTIONALLY_EMPTY,
    ITEM_ARMOR_EQUIP_CHAIN,
    ITEM_ARMOR_EQUIP_DIAMOND,
    ITEM_ARMOR_EQUIP_ELYTRA,
    ITEM_ARMOR_EQUIP_GENERIC,
    ITEM_ARMOR_EQUIP_GOLD,
    ITEM_ARMOR_EQUIP_IRON,
    ITEM_ARMOR_EQUIP_LEATHER,
    ITEM_ARMOR_EQUIP_NETHERITE,
    ITEM_ARMOR_EQUIP_TURTLE,
    ITEM_AXE_SCRAPE,
    ITEM_AXE_STRIP,
    ITEM_AXE_WAX_OFF,
    ITEM_BONE_MEAL_USE,
    ITEM_BOOK_PAGE_TURN,
    ITEM_BOOK_PUT,
    ITEM_BOTTLE_EMPTY,
    ITEM_BOTTLE_FILL,
    ITEM_BOTTLE_FILL_DRAGONBREATH,
    /**
     * Removed in v1.20
     */
    ITEM_BRUSH_BRUSHING,
    ITEM_BRUSH_BRUSHING_GENERIC,
    ITEM_BRUSH_BRUSHING_GRAVEL,
    ITEM_BRUSH_BRUSHING_GRAVEL_COMPLETE,
    ITEM_BRUSH_BRUSHING_SAND,
    ITEM_BRUSH_BRUSHING_SAND_COMPLETE,
    /**
     * Removed in v1.20
     */
    ITEM_BRUSH_BRUSH_SAND_COMPLETED,
    ITEM_BUCKET_EMPTY,
    ITEM_BUCKET_EMPTY_AXOLOTL,
    ITEM_BUCKET_EMPTY_FISH,
    ITEM_BUCKET_EMPTY_LAVA,
    ITEM_BUCKET_EMPTY_POWDER_SNOW,
    ITEM_BUCKET_EMPTY_TADPOLE,
    ITEM_BUCKET_FILL,
    ITEM_BUCKET_FILL_AXOLOTL,
    ITEM_BUCKET_FILL_FISH,
    ITEM_BUCKET_FILL_LAVA,
    ITEM_BUCKET_FILL_POWDER_SNOW,
    ITEM_BUCKET_FILL_TADPOLE,
    ITEM_BUNDLE_DROP_CONTENTS,
    ITEM_BUNDLE_INSERT,
    ITEM_BUNDLE_REMOVE_ONE,
    ITEM_CHORUS_FRUIT_TELEPORT,
    ITEM_CROP_PLANT,
    ITEM_CROSSBOW_HIT,
    ITEM_CROSSBOW_LOADING_END,
    ITEM_CROSSBOW_LOADING_MIDDLE,
    ITEM_CROSSBOW_LOADING_START,
    ITEM_CROSSBOW_QUICK_CHARGE_1,
    ITEM_CROSSBOW_QUICK_CHARGE_2,
    ITEM_CROSSBOW_QUICK_CHARGE_3,
    ITEM_CROSSBOW_SHOOT,
    ITEM_DYE_USE,
    ITEM_ELYTRA_FLYING,
    ITEM_FIRECHARGE_USE,
    ITEM_FLINTANDSTEEL_USE("FIRE_IGNITE"),
    ITEM_GLOW_INK_SAC_USE,
    ITEM_GOAT_HORN_PLAY,
    ITEM_GOAT_HORN_SOUND_0,
    ITEM_GOAT_HORN_SOUND_1,
    ITEM_GOAT_HORN_SOUND_2,
    ITEM_GOAT_HORN_SOUND_3,
    ITEM_GOAT_HORN_SOUND_4,
    ITEM_GOAT_HORN_SOUND_5,
    ITEM_GOAT_HORN_SOUND_6,
    ITEM_GOAT_HORN_SOUND_7,
    ITEM_HOE_TILL,
    ITEM_HONEYCOMB_WAX_ON,
    ITEM_HONEY_BOTTLE_DRINK,
    ITEM_INK_SAC_USE,
    ITEM_LODESTONE_COMPASS_LOCK,
    ITEM_NETHER_WART_PLANT,
    ITEM_SHIELD_BLOCK,
    ITEM_SHIELD_BREAK,
    ITEM_SHOVEL_FLATTEN,
    ITEM_SPYGLASS_STOP_USING,
    ITEM_SPYGLASS_USE,
    ITEM_TOTEM_USE,
    ITEM_TRIDENT_HIT,
    ITEM_TRIDENT_HIT_GROUND,
    ITEM_TRIDENT_RETURN,
    ITEM_TRIDENT_RIPTIDE_1,
    ITEM_TRIDENT_RIPTIDE_2("ITEM_TRIDENT_RIPTIDE_1"),
    ITEM_TRIDENT_RIPTIDE_3("ITEM_TRIDENT_RIPTIDE_1"),
    ITEM_TRIDENT_THROW,
    ITEM_TRIDENT_THUNDER,
    MUSIC_CREATIVE,
    MUSIC_CREDITS,
    MUSIC_DISC_11("RECORD_11"),
    MUSIC_DISC_13("RECORD_13"),
    MUSIC_DISC_5,
    MUSIC_DISC_BLOCKS("RECORD_BLOCKS"),
    MUSIC_DISC_CAT("RECORD_CAT"),
    MUSIC_DISC_CHIRP("RECORD_CHIRP"),
    MUSIC_DISC_FAR("RECORD_FAR"),
    MUSIC_DISC_MALL("RECORD_MALL"),
    MUSIC_DISC_MELLOHI("RECORD_MELLOHI"),
    MUSIC_DISC_OTHERSIDE,
    MUSIC_DISC_PIGSTEP,
    MUSIC_DISC_RELIC,
    MUSIC_DISC_STAL("RECORD_STAL"),
    MUSIC_DISC_STRAD("RECORD_STRAD"),
    MUSIC_DISC_WAIT("RECORD_WAIT"),
    MUSIC_DISC_WARD("RECORD_WARD"),
    MUSIC_DRAGON,
    MUSIC_END,
    MUSIC_GAME,
    MUSIC_MENU,
    MUSIC_NETHER_BASALT_DELTAS("MUSIC_NETHER"),
    MUSIC_NETHER_CRIMSON_FOREST,
    MUSIC_NETHER_NETHER_WASTES,
    MUSIC_NETHER_SOUL_SAND_VALLEY,
    MUSIC_NETHER_WARPED_FOREST,
    MUSIC_OVERWORLD_BADLANDS,
    MUSIC_OVERWORLD_BAMBOO_JUNGLE,
    MUSIC_OVERWORLD_CHERRY_GROVE,
    MUSIC_OVERWORLD_DEEP_DARK,
    MUSIC_OVERWORLD_DESERT,
    MUSIC_OVERWORLD_DRIPSTONE_CAVES,
    MUSIC_OVERWORLD_FLOWER_FOREST,
    MUSIC_OVERWORLD_FOREST,
    MUSIC_OVERWORLD_FROZEN_PEAKS,
    MUSIC_OVERWORLD_GROVE,
    MUSIC_OVERWORLD_JAGGED_PEAKS,
    MUSIC_OVERWORLD_JUNGLE,
    /**
     * Removed in v1.20
     */
    MUSIC_OVERWORLD_JUNGLE_AND_FOREST,
    MUSIC_OVERWORLD_LUSH_CAVES,
    MUSIC_OVERWORLD_MEADOW,
    MUSIC_OVERWORLD_OLD_GROWTH_TAIGA,
    MUSIC_OVERWORLD_SNOWY_SLOPES,
    MUSIC_OVERWORLD_SPARSE_JUNGLE,
    MUSIC_OVERWORLD_STONY_PEAKS,
    MUSIC_OVERWORLD_SWAMP,
    MUSIC_UNDER_WATER,
    PARTICLE_SOUL_ESCAPE,
    UI_BUTTON_CLICK("CLICK"),
    UI_CARTOGRAPHY_TABLE_TAKE_RESULT,
    UI_LOOM_SELECT_PATTERN,
    UI_LOOM_TAKE_RESULT,
    UI_STONECUTTER_SELECT_RECIPE,
    UI_STONECUTTER_TAKE_RESULT,
    UI_TOAST_CHALLENGE_COMPLETE,
    UI_TOAST_IN,
    UI_TOAST_OUT,
    WEATHER_RAIN("AMBIENCE_RAIN"),
    WEATHER_RAIN_ABOVE;

    /**
     * Cached list of {@link XSound#values()} to avoid allocating memory for
     * calling the method every time.
     *
     * @since 2.0.0
     */
    public static final XSound[] VALUES = values();

    public static final float DEFAULT_VOLUME = 1.0f, DEFAULT_PITCH = 1.0f;

    @Nullable
    private final Sound sound;

    XSound(@Nonnull String... legacies) {
        Sound bukkitSound = Data.BUKKIT_NAMES.get(this.name());
        if (bukkitSound == null) {
            for (String legacy : legacies) {
                bukkitSound = Data.BUKKIT_NAMES.get(legacy);
                if (bukkitSound != null) break;
            }
        }
        this.sound = bukkitSound;

        Data.NAMES.put(this.name(), this);
        for (String legacy : legacies) {
            Data.NAMES.putIfAbsent(legacy, this);
        }
    }

    /**
     * Attempts to build the string like an enum name.<br>
     * Removes all the spaces, numbers and extra non-English characters. Also removes some config/in-game based strings.
     * While this method is hard to maintain, it's extremely efficient. It's approximately more than x5 times faster than
     * the normal RegEx + String Methods approach for both formatted and unformatted material names.
     *
     * @param name the sound name to format.
     * @return an enum name.
     * @since 1.0.0
     */
    @Nonnull
    private static String format(@Nonnull String name) {
        int len = name.length();
        char[] chs = new char[len];
        int count = 0;
        boolean appendUnderline = false;

        for (int i = 0; i < len; i++) {
            char ch = name.charAt(i);

            if (!appendUnderline && count != 0 && (ch == '-' || ch == ' ' || ch == '_') && chs[count] != '_')
                appendUnderline = true;
            else {
                boolean number = false;
                // A few sounds have numbers in them.
                if ((ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || (number = (ch >= '0' && ch <= '9'))) {
                    if (appendUnderline) {
                        chs[count++] = '_';
                        appendUnderline = false;
                    }

                    if (number) chs[count++] = ch;
                    else chs[count++] = (char) (ch & 0x5f);
                }
            }
        }

        return new String(chs, 0, count);
    }

    /**
     * Parses the XSound with the given name.
     *
     * @param sound the name of the sound.
     * @return a matched XSound.
     * @since 1.0.0
     */
    @Nonnull
    public static Optional<XSound> matchXSound(@Nonnull String sound) {
        if (sound == null || sound.isEmpty())
            throw new IllegalArgumentException("Cannot match XSound of a null or empty sound name");
        return Optional.ofNullable(Data.NAMES.get(format(sound)));
    }

    /**
     * Parses the XSound with the given bukkit sound.
     *
     * @param sound the Bukkit sound.
     * @return a matched sound.
     * @throws IllegalArgumentException may be thrown as an unexpected exception.
     * @since 2.0.0
     */
    @Nonnull
    public static XSound matchXSound(@Nonnull Sound sound) {
        Objects.requireNonNull(sound, "Cannot match XSound of a null sound");
        return Objects.requireNonNull(Data.NAMES.get(sound.name()), () -> "Unsupported sound: " + sound.name());
    }

    private static List<String> split(@Nonnull String str, @SuppressWarnings("SameParameterValue") char separatorChar) {
        List<String> list = new ArrayList<>(5);
        boolean match = false, lastMatch = false;
        int len = str.length();
        int start = 0;

        for (int i = 0; i < len; i++) {
            if (str.charAt(i) == separatorChar) {
                if (match) {
                    list.add(str.substring(start, i));
                    match = false;
                    lastMatch = true;
                }

                // This is important, it should not be i++
                start = i + 1;
                continue;
            }

            lastMatch = false;
            match = true;
        }

        if (match || lastMatch) {
            list.add(str.substring(start, len));
        }
        return list;
    }

    /**
     * Just an extra feature that loads sounds from strings.
     * Useful for getting sounds from config files.
     * Sounds are thread safe.
     * <p>
     * It's strongly recommended to use this method while using it inside a loop.
     * This can help to avoid parsing the sound properties multiple times.
     * A simple usage of using it in a loop is:
     * <blockquote><pre>
     *     Record record = XSound.parse(player, location, sound, false).join();
     *     // Loop:
     *     if (record != null) record.play();
     * </pre></blockquote>
     * <p>
     * This will also ignore {@code none} and {@code null} strings.
     * <p>
     * <b>Format:</b> [~]Sound, [Volume], [Pitch]<br>
     * Where {@code ~} prefix will play the sound at the location even if a player is specified.
     * A sound played at a location will be heard by everyone around.
     * <p>
     * <b>Examples:</b>
     * <p>
     * <pre>
     *     ~ENTITY_PLAYER_BURP, 2.5f, 0.5
     *     ENTITY_PLAYER_BURP, 0.5, 1f
     *     BURP, 0.5f, 1
     *     MUSIC_END, 10f
     *     ~MUSIC_END, 10
     *     none (case-insensitive)
     *     null (~ in yml)
     * </pre>
     * <p>
     *
     * @param sound the string of the sound with volume and pitch (if needed).
     * @since 7.0.0
     */
    @Nullable
    public static Record parse(@Nullable String sound) {
        if (Strings.isNullOrEmpty(sound) || sound.equalsIgnoreCase("none")) return null;
        @SuppressWarnings("DynamicRegexReplaceableByCompiledPattern") List<String> split = split(sound.replace(" ", ""), ',');

        String name = split.get(0);
        boolean playAtLocation;
        if (name.charAt(0) == '~') {
            name = name.substring(1);
            playAtLocation = true;
        } else playAtLocation = false;

        if (name.isEmpty()) throw new IllegalArgumentException("No sound name specified: " + sound);
        Optional<XSound> soundType = matchXSound(name);
        if (!soundType.isPresent()) throw new IllegalArgumentException("Unknown sound: " + name);

        float volume = DEFAULT_VOLUME;
        float pitch = DEFAULT_PITCH;

        try {
            if (split.size() > 1) volume = Float.parseFloat(split.get(1));
        } catch (NumberFormatException ex) {
            throw new NumberFormatException("Invalid number '" + split.get(1) + "' for sound volume '" + sound + '\'');
        }
        try {
            if (split.size() > 2) pitch = Float.parseFloat(split.get(2));
        } catch (NumberFormatException ex) {
            throw new NumberFormatException("Invalid number '" + split.get(2) + "' for sound pitch '" + sound + '\'');
        }

        return new Record(soundType.get(), null, null, volume, pitch, playAtLocation);
    }

    /**
     * Stops all the playing musics (not all the sounds)
     * <p>
     * Note that this method will only work for the sound
     * that are sent from {@link Player#playSound} and
     * the sounds played from the client will not be
     * affected by this.
     *
     * @param player the player to stop all the sounds from.
     * @see #stopSound(Player)
     * @since 2.0.0
     */
    public static void stopMusic(@Nonnull Player player) {
        Objects.requireNonNull(player, "Cannot stop playing musics from null player");

        // We don't need to cache because it's rarely used.
        XSound[] musics = {
                MUSIC_CREATIVE, MUSIC_CREDITS,
                MUSIC_DISC_11, MUSIC_DISC_13, MUSIC_DISC_BLOCKS, MUSIC_DISC_CAT, MUSIC_DISC_CHIRP,
                MUSIC_DISC_FAR, MUSIC_DISC_MALL, MUSIC_DISC_MELLOHI, MUSIC_DISC_STAL,
                MUSIC_DISC_STRAD, MUSIC_DISC_WAIT, MUSIC_DISC_WARD,
                MUSIC_DRAGON, MUSIC_END, MUSIC_GAME, MUSIC_MENU, MUSIC_NETHER_BASALT_DELTAS, MUSIC_UNDER_WATER,
                MUSIC_NETHER_CRIMSON_FOREST, MUSIC_NETHER_WARPED_FOREST
        };

        for (XSound music : musics) {
            Sound sound = music.parseSound();
            if (sound != null) player.stopSound(sound);
        }
    }

    /**
     * Plays an instrument's notes in an ascending form.
     * This method is not really relevant to this utility class, but a nice feature.
     *
     * @param plugin      the plugin handling schedulers.
     * @param player      the player to play the note from.
     * @param playTo      the entity to play the note to.
     * @param instrument  the instrument.
     * @param ascendLevel the ascend level of notes. Can only be positive and not higher than 7
     * @param delay       the delay between each play.
     * @return the async task handling the operation.
     * @since 2.0.0
     */
    @Nonnull
    public static BukkitTask playAscendingNote(@Nonnull Plugin plugin, @Nonnull Player player, @Nonnull Entity playTo, @Nonnull Instrument instrument,
                                               int ascendLevel, int delay) {
        Objects.requireNonNull(player, "Cannot play note from null player");
        Objects.requireNonNull(playTo, "Cannot play note to null entity");

        if (ascendLevel <= 0) throw new IllegalArgumentException("Note ascend level cannot be lower than 1");
        if (ascendLevel > 7) throw new IllegalArgumentException("Note ascend level cannot be greater than 7");
        if (delay <= 0) throw new IllegalArgumentException("Delay ticks must be at least 1");

        return new BukkitRunnable() {
            int repeating = ascendLevel;

            @Override
            public void run() {
                player.playNote(playTo.getLocation(), instrument, Note.natural(1, Note.Tone.values()[ascendLevel - repeating]));
                if (repeating-- == 0) cancel();
            }
        }.runTaskTimerAsynchronously(plugin, 0, delay);
    }

    /**
     * In most cases you should be using {@link #name()} instead.
     *
     * @return a friendly readable string name.
     */
    @Override
    public String toString() {
        return Arrays.stream(name().split("_"))
                .map(t -> t.charAt(0) + t.substring(1).toLowerCase())
                .collect(Collectors.joining(" "));
    }

    /**
     * Parses the XSound as a {@link Sound} based on the server version.
     *
     * @return the vanilla sound.
     * @since 1.0.0
     */
    @Nullable
    public Sound parseSound() {
        return this.sound;
    }

    /**
     * Checks if this sound is supported in the current Minecraft version.
     * <p>
     * An invocation of this method yields exactly the same result as the expression:
     * <p>
     * <blockquote>
     * {@link #parseSound()} != null
     * </blockquote>
     *
     * @return true if the current version has this sound, otherwise false.
     * @since 1.0.0
     */
    public boolean isSupported() {
        return this.parseSound() != null;
    }

    /**
     * Plays a sound repeatedly with the given delay at a moving target's location.
     *
     * @param plugin the plugin handling schedulers. (You can replace this with a static instance)
     * @param entity the entity to play the sound to. We exactly need an entity to keep the track of location changes.
     * @param volume the volume of the sound.
     * @param pitch  the pitch of the sound.
     * @param repeat the amount of times to repeat playing.
     * @param delay  the delay between each repeat.
     * @return the async task handling this operation.
     * @see #play(Location, float, float)
     * @since 2.0.0
     */
    @Nonnull
    public BukkitTask playRepeatedly(@Nonnull Plugin plugin, @Nonnull Entity entity, float volume, float pitch, int repeat, int delay) {
        return playRepeatedly(plugin, Collections.singleton(entity), volume, pitch, repeat, delay);
    }

    /**
     * Plays a sound repeatedly with the given delay at moving targets' locations.
     *
     * @param plugin   the plugin handling schedulers. (You can replace this with a static instance)
     * @param entities the entities to play the sound to. We exactly need the entities to keep the track of location changes.
     * @param volume   the volume of the sound.
     * @param pitch    the pitch of the sound.
     * @param repeat   the amount of times to repeat playing.
     * @param delay    the delay between each repeat.
     * @return the async task handling this operation.
     * @see #play(Location, float, float)
     * @since 2.0.0
     */
    @Nonnull
    public BukkitTask playRepeatedly(@Nonnull Plugin plugin, @Nonnull Iterable<? extends Entity> entities, float volume, float pitch, int repeat, int delay) {
        Objects.requireNonNull(plugin, "Cannot play repeating sound from null plugin");
        Objects.requireNonNull(entities, "Cannot play repeating sound at null locations");

        if (repeat <= 0) throw new IllegalArgumentException("Cannot repeat playing sound " + repeat + " times");
        if (delay <= 0) throw new IllegalArgumentException("Delay ticks must be at least 1");

        return new BukkitRunnable() {
            int repeating = repeat;

            @Override
            public void run() {
                for (Entity entity : entities) {
                    play(entity.getLocation(), volume, pitch);
                }

                if (repeating-- == 0) cancel();
            }
        }.runTaskTimer(plugin, 0, delay);
    }

    /**
     * Stops playing the specified sound from the player.
     *
     * @param player the player to stop playing the sound to.
     * @see #stopMusic(Player)
     * @since 2.0.0
     */
    public void stopSound(@Nonnull Player player) {
        Objects.requireNonNull(player, "Cannot stop playing sound from null player");
        Sound sound = this.parseSound();
        if (sound != null) player.stopSound(sound);
    }

    /**
     * A quick async way to play a sound from the config.
     *
     * @param player the player to play the sound to.
     * @param sound  the sound to play to the player.
     * @see #play(Location, String)
     * @since 1.0.0
     */
    @Nonnull
    @Deprecated
    public static CompletableFuture<Record> play(@Nonnull Player player, @Nullable String sound) {
        Objects.requireNonNull(player, "Cannot play sound to null player");
        return CompletableFuture.supplyAsync(() -> {
            Record record;
            try {
                record = parse(sound);
            } catch (Throwable ex) {
                return null;
            }
            if (record == null) return null;
            record.forPlayer(player).play();
            return record;
        }).exceptionally(x -> {
            x.printStackTrace();
            return null;
        });
    }

    /**
     * A quick async way to play a sound from the config.
     *
     * @see #play(Location, String)
     * @since 3.0.0
     */
    @Nullable
    public static Record play(@Nonnull Location location, @Nullable String sound) {
        Objects.requireNonNull(location, "Cannot play sound to null location");
        Record record = parse(sound);
        if (record == null) return null;
        record.atLocation(location).play();
        return record;
    }

    /**
     * Plays a normal sound to an entity.
     *
     * @param entity the entity to play the sound to.
     * @since 1.0.0
     */
    public void play(@Nonnull Entity entity) {
        play(entity, DEFAULT_VOLUME, DEFAULT_PITCH);
    }

    /**
     * Plays a sound to an entity with the given volume and pitch.
     *
     * @param entity the entity to play the sound to.
     * @param volume the volume of the sound, 1 is normal.
     * @param pitch  the pitch of the sound, 0 is normal.
     * @since 1.0.0
     */
    public void play(@Nonnull Entity entity, float volume, float pitch) {
        Objects.requireNonNull(entity, "Cannot play sound to a null entity");
        if (entity instanceof Player) {
            Sound sound = this.parseSound();
            if (sound != null) ((Player) entity).playSound(entity.getLocation(), sound, volume, pitch);
        } else {
            play(entity.getLocation(), volume, pitch);
        }
    }

    /**
     * Plays a normal sound in a location.
     *
     * @param location the location to play the sound in.
     * @since 2.0.0
     */
    public void play(@Nonnull Location location) {
        play(location, DEFAULT_VOLUME, DEFAULT_PITCH);
    }

    /**
     * Plays a sound in a location with the given volume and pitch.
     *
     * @param location the location to play this sound.
     * @param volume   the volume of the sound, 1 is normal.
     * @param pitch    the pitch of the sound, 0 is normal.
     * @since 2.0.0
     */
    public void play(@Nonnull Location location, float volume, float pitch) {
        Objects.requireNonNull(location, "Cannot play sound to null location");
        Sound sound = this.parseSound();
        if (sound != null) location.getWorld().playSound(location, sound, volume, pitch);
    }

    /**
     * Used for data that need to be accessed during enum initialization.
     *
     * @since 5.0.0
     */
    private static final class Data {
        /**
         * Just for enum initialization.
         *
         * @since 5.0.0
         */
        private static final WeakHashMap<String, Sound> BUKKIT_NAMES = new WeakHashMap<>();
        /**
         * We don't want to use {@link Enums#getIfPresent(Class, String)} to avoid a few checks.
         *
         * @since 3.1.0
         */
        private static final Map<String, XSound> NAMES = new HashMap<>();

        static {
            for (Sound sound : Sound.values()) BUKKIT_NAMES.put(sound.name(), sound);
        }
    }

    /**
     * A class to help caching sound properties parsed from config.
     *
     * @since 3.0.0
     */
    public static class Record implements Cloneable {
        @Nonnull
        public final XSound sound;
        public final float volume, pitch;
        public boolean playAtLocation;
        @Nullable
        public Player player;
        @Nullable
        public Location location;

        public Record(@Nonnull XSound sound) {
            this(sound, DEFAULT_VOLUME, DEFAULT_PITCH);
        }

        public Record(@Nonnull XSound sound, float volume, float pitch) {
            this(sound, null, null, volume, pitch, false);
        }

        public Record(@Nonnull XSound sound, @Nullable Player player, @Nullable Location location, float volume, float pitch, boolean playAtLocation) {
            this.sound = Objects.requireNonNull(sound, "Sound cannot be null");
            this.player = player;
            this.location = location;
            this.volume = volume;
            this.pitch = pitch;
            this.playAtLocation = playAtLocation;
        }

        /**
         * Plays the sound only for a single player and no one else can hear it.
         */
        public Record forPlayer(@Nullable Player player) {
            this.player = player;
            return this;
        }

        /**
         * Plays the sound to all the nearby players (based on the specified volume)
         */
        public Record atLocation(@Nullable Location location) {
            this.location = location;
            return this;
        }

        /**
         * Plays the sound only for a single player and no on else can hear it.
         * The source of the sound is different and players using headphones may
         * hear the sound with a <a href="https://en.wikipedia.org/wiki/3D_audio_effect">3D audio effect</a>.
         */
        public Record forPlayerAtLocation(@Nullable Player player, @Nullable Location location) {
            this.player = player;
            this.location = location;
            return this;
        }

        /**
         * Plays the sound with the given options and updating the player's location.
         *
         * @since 3.0.0
         */
        public void play() {
            if (player == null && location == null)
                throw new IllegalStateException("Cannot play sound when there is no location available");
            play(player == null ? location : player.getLocation());
        }

        /**
         * Plays the sound with the updated location.
         *
         * @param updatedLocation the updated location.
         * @since 3.0.0
         */
        public void play(@Nonnull Location updatedLocation) {
            Objects.requireNonNull(updatedLocation, "Cannot play sound at null location");
            if (playAtLocation || player == null) {
                location.getWorld().playSound(updatedLocation, sound.parseSound(), volume, pitch);
            } else {
                player.playSound(updatedLocation, sound.parseSound(), volume, pitch);
            }
        }

        /**
         * Stops the sound playing to the players that this sound was played to.
         * Note this works fine if the sound was played to one specific player, but for
         * location-based sounds this only works if the players were within the same range as the original
         * volume level.
         * <p>
         * If this is a critical issue you can extend this class and add a cache for all the players that heard the sound.
         *
         * @since 7.0.2
         */
        public void stopSound() {
            if (playAtLocation) {
                for (Entity entity : location.getWorld().getNearbyEntities(location, volume, volume, volume)) {
                    if (entity instanceof Player) ((Player) entity).stopSound(sound.parseSound());
                }
            }
            if (player != null) player.stopSound(sound.parseSound());
        }

        public String rebuild() {
            return (playAtLocation ? "~" : "") + sound.sound + ", " + volume + ", " + pitch;
        }

        @SuppressWarnings("MethodDoesntCallSuperMethod")
        @Override
        public Record clone() {
            return new Record(
                    sound,
                    player,
                    location,
                    volume,
                    pitch,
                    playAtLocation
            );
        }
    }
}
